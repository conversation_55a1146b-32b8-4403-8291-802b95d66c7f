FROM maven:3.6.3-openjdk-11-slim AS builder

ENV SRC_ROOT=/home/

WORKDIR ${SRC_ROOT}

COPY sample_event_listener/ .

RUN mvn package



#FROM jboss/keycloak:13.0.0
FROM quay.io/keycloak/keycloak:19.0.0-legacy
MAINTAINER Questmarine
COPY --from=builder --chown=jboss:jboss /home/<USER>/sample-event-listener-1.0.0.jar /opt/jboss/keycloak/providers/
COPY /adc.json /opt/jboss/keycloak/adc.json
COPY /insurer-app/ /opt/jboss/keycloak/themes/insurer-app/
COPY /construction-app/ /opt/jboss/keycloak/themes/construction-app/
COPY /property-app/ /opt/jboss/keycloak/themes/property-app/
COPY /quest-cargo/ /opt/jboss/keycloak/themes/quest-cargo/
COPY /quest-test/ /opt/jboss/keycloak/themes/quest-test/
COPY /quest-marine-v2/ /opt/jboss/keycloak/themes/quest-marine-v2/
COPY /realm-export.json /tmp/realm-export.json
COPY /aviation-app/ /opt/jboss/keycloak/themes/aviation-app/
COPY /aviation-app-v2/ /opt/jboss/keycloak/themes/aviation-app-v2/
COPY /pv&t/ /opt/jboss/keycloak/themes/pv&t/
COPY /api-portal/ /opt/jboss/keycloak/themes/api-portal/
COPY /cip-app/ /opt/jboss/keycloak/themes/cip-app/
COPY /logistics/ /opt/jboss/keycloak/themes/logistics/