<#outputformat "plainText">
<#assign requiredActionsText><#if requiredActions??><#list requiredActions><#items as reqActionItem>${msg("requiredAction.${reqActionItem}")}<#sep>, </#sep></#items></#list></#if></#assign>
</#outputformat>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Your Account Setup</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007FAE; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #007FAE; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Concirrus</h1>
        </div>
        <div class="content">
            <h2>Complete Your Account Setup</h2>
            <p>Hi ${user.firstName!''},</p>
            <p>Your account has been created and requires some additional setup.</p>
            <#if requiredActionsText?has_content>
                <p>Please complete the following actions: <strong>${requiredActionsText}</strong></p>
            </#if>
            <p>Click the button below to complete your account setup:</p>
            <a href="${link}" class="button">Complete Setup</a>
            <p>This link will expire in ${linkExpiration} minutes.</p>
            <p>If you're having trouble clicking the button, copy and paste the URL below into your web browser:</p>
            <p style="word-break: break-all;">${link}</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 Concirrus. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
