<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('password','password-confirm'); section>
    <#if section = "header">
        Update Your Password
    <#elseif section = "form">
        <form id="kc-passwd-update-form" action="${url.loginAction}" method="post">
            <input type="text" 
                   id="username" 
                   name="username" 
                   value="${username}" 
                   autocomplete="username" 
                   readonly="readonly" 
                   style="display:none;" />
            <input type="password" 
                   id="password" 
                   name="password" 
                   autocomplete="current-password" 
                   style="display:none;" />

            <div class="info-text">
                Choose a new password for your account.
            </div>

            <div class="password-container">
                <input type="password" 
                       id="password-new" 
                       name="password-new" 
                       placeholder="New Password" 
                       autofocus 
                       autocomplete="new-password" 
                       required />
                <img src="${url.resourcesPath}/img/mdi_eye-off.png"
                     alt="Show Password" 
                     class="toggle-password toggle-new-password"
                     data-eye-open="${url.resourcesPath}/img/mdi_eye.png"
                     data-eye-closed="${url.resourcesPath}/img/mdi_eye-off.png" />
            </div>

            <div class="password-container">
                <input type="password" 
                       id="password-confirm" 
                       name="password-confirm" 
                       placeholder="Confirm New Password" 
                       autocomplete="new-password" 
                       required />
                <img src="${url.resourcesPath}/img/mdi_eye-off.png"
                     alt="Show Password" 
                     class="toggle-password toggle-confirm-password"
                     data-eye-open="${url.resourcesPath}/img/mdi_eye.png"
                     data-eye-closed="${url.resourcesPath}/img/mdi_eye-off.png" />
            </div>

            <div id="password-requirements" class="password-requirements">
                <div class="requirement" id="length-req">
                    <span class="check">✓</span>
                    <span class="cross">✗</span>
                    At least 8 characters
                </div>
                <div class="requirement" id="lowercase-req">
                    <span class="check">✓</span>
                    <span class="cross">✗</span>
                    One lowercase letter
                </div>
                <div class="requirement" id="uppercase-req">
                    <span class="check">✓</span>
                    <span class="cross">✗</span>
                    One uppercase letter
                </div>
                <div class="requirement" id="number-req">
                    <span class="check">✓</span>
                    <span class="cross">✗</span>
                    One number
                </div>
                <div class="requirement" id="special-req">
                    <span class="check">✓</span>
                    <span class="cross">✗</span>
                    One special character
                </div>
            </div>

            <button type="submit" id="update-password-btn" disabled>Update Password</button>
        </form>
    </#if>
</@layout.registrationLayout>
