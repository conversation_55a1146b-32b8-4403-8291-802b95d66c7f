<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username','password') displayInfo=realm.password && realm.registrationAllowed && !registrationDisabled??; section>
    <#if section = "header">
        Welcome to Concirrus API Doc Handbook
    <#elseif section = "form">
        <#if realm.password>
            <form id="kc-form-login" action="${url.loginAction}" method="post">
                <input type="text" 
                       placeholder="Username or Email" 
                       name="username" 
                       id="username" 
                       value="${(login.username)!''}" 
                       required 
                       autofocus />
                
                <div class="password-container">
                    <input type="password" 
                           placeholder="Password" 
                           name="password" 
                           id="password" 
                           required />
                    <img src="${url.resourcesPath}/img/mdi_eye-off.png"
                         alt="Show Password" 
                         class="toggle-password"
                         id="togglePassword"
                         data-eye-open="${url.resourcesPath}/img/mdi_eye.png"
                         data-eye-closed="${url.resourcesPath}/img/mdi_eye-off.png" />
                </div>
                
                <#if realm.resetPasswordAllowed>
                    <div class="forgot-password">
                        <a href="${url.loginResetCredentialsUrl}">Forgot Password?</a>
                    </div>
                </#if>
                
                <button type="submit">Login</button>
            </form>
        </#if>
    <#elseif section = "info">
        <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
            <div class="register-link">
                <span>New user? <a href="${url.registrationUrl}">Register</a></span>
            </div>
        </#if>
    </#if>
</@layout.registrationLayout>
