doLogIn=Login
doRegister=Register
doCancel=Cancel
doSubmit=Submit
doYes=Yes
doNo=No
doContinue=Continue
doIgnore=Ignore
doAccept=Accept
doDecline=Decline
loginInputPlaceholder=Username or Email
passwordInputPlaceholder=Password
doForgotPassword=Forgot Password?
doClickHere=Click here
doImpersonate=Impersonate

registerTitle=Register
loginTitle=API Portal Login
loginTitleHtml={0}
impersonateTitle={0} Impersonate User
impersonateTitleHtml=<strong>{0}</strong> Impersonate User
realmChoice=Realm
unknownUser=Unknown user
loginTotpTitle=Mobile Authenticator Setup
loginProfileTitle=Update Account Information
loginTimeout=You took too long to login. Login process starting from beginning.
oauthGrantTitle=Grant Access to {0}
oauthGrantTitleHtml={0}
errorTitle=We''re sorry...
errorTitleHtml=We''re <strong>sorry</strong> ...
emailVerifyTitle=Email verification
emailForgotTitle=Reset Your Password
updatePasswordTitle=Update Your Password
codeSuccessTitle=Success code
codeErrorTitle=Error code\: {0}

termsTitle=Terms and Conditions
termsText=<p>Terms and conditions to be defined</p>
termsPlainText=Terms and conditions to be defined.

recaptchaFailed=Invalid Recaptcha
recaptchaNotConfigured=Recaptcha is required, but not configured
consentDenied=Consent denied.

noAccount=New user?
username=Username
usernameOrEmail=Username or email
firstName=First name
givenName=Given name
fullName=Full name
lastName=Last name
familyName=Family name
email=Email
password=Password
passwordConfirm=Confirm password
passwordNew=New Password
passwordNewConfirm=Confirm password
resetPasswordHeader=Choose a new password for your account
rememberMe=Remember me
authenticatorCode=One-time code

emailInstruction=Enter your email address and we''ll send you a link to reset your password.
backToLogin=â Back to Login

invalidUserMessage=Invalid username or password
invalidEmailMessage=Invalid email address
accountDisabledMessage=Account is disabled, contact admin
accountTemporarilyDisabledMessage=Account is temporarily disabled, contact admin or try again later
expiredCodeMessage=Login timeout. Please login again
expiredActionMessage=Action expired. Please continue with login now
expiredActionTokenNoSessionMessage=Your password reset link has expired
expiredActionTokenSessionExistsMessage=Action expired. Please start again

missingFirstNameMessage=Please specify first name
missingLastNameMessage=Please specify last name
missingEmailMessage=Please specify email
missingUsernameMessage=Please specify username
missingPasswordMessage=Please specify password
missingTotpMessage=Please specify authenticator code
notMatchPasswordMessage=Passwords do not match

invalidPasswordExistingMessage=Invalid existing password
invalidPasswordBlacklistedMessage=Invalid password: password is blacklisted
invalidPasswordConfirmMessage=Password confirmation doesn''t match
invalidTotpMessage=Invalid authenticator code

usernameExistsMessage=Username already exists
emailExistsMessage=Email already exists

emailSentMessage=You should receive an email shortly with further instructions
emailSendErrorMessage=Failed to send email, please try again later

accountUpdatedMessage=Your password has been reset successfully
accountPasswordUpdatedMessage=Your password has been updated

invalidPasswordMinLengthMessage=Invalid password: minimum length {0}
invalidPasswordMinDigitsMessage=Invalid password: must contain at least {0} numerical digits
invalidPasswordMinLowerCaseCharsMessage=Invalid password: must contain at least {0} lower case characters
invalidPasswordMinUpperCaseCharsMessage=Invalid password: must contain at least {0} upper case characters
invalidPasswordMinSpecialCharsMessage=Invalid password: must contain at least {0} special characters
invalidPasswordNotUsernameMessage=Invalid password: must not be equal to the username
invalidPasswordRegexPatternMessage=Invalid password: fails to match regex pattern(s)
invalidPasswordHistoryMessage=Invalid password: must not be equal to any of last {0} passwords
invalidPasswordGenericMessage=Invalid password: new password doesn''t match password policies

requiredAction.CONFIGURE_TOTP=Configure OTP
requiredAction.terms_and_conditions=Terms and Conditions
requiredAction.UPDATE_PASSWORD=Update Password
requiredAction.UPDATE_PROFILE=Update Profile
requiredAction.VERIFY_EMAIL=Verify Email
