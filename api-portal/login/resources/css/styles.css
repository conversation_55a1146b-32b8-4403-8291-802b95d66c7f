html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Arial, sans-serif;
    background-image: url('../img/Landing_page.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: relative;
}

.title-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 32px;
}

.title-wrapper h2 {
    margin: 0;
    color: #fff;
    font-size: 2.2rem;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.logo {
    display: flex;
    align-items: center;
    position: absolute;
    top: 32px;
    left: 40px;
    z-index: 2;
}

.logo h1 {
    margin: 0;
    margin-right: 10px;
    font-size: 2em;
    color: #fff;
    position: relative;
    top: 5px;
    font-family: 'Gotham Medium', <PERSON><PERSON>, sans-serif;
    font-weight: 500;
}

.logo img {
    height: 36px;
}

.center-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 100%;
    max-width: 400px;
}

.center-content h1 {
    color: #fff;
    font-size: 2.2rem;
    margin-bottom: 32px;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
}

input {
    padding: 14px 16px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 0;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    outline: none;
    transition: box-shadow 0.2s;
}

input:focus {
    box-shadow: 0 0 0 2px #2ebf91;
}

button {
    padding: 14px 0;
    border: none;
    border-radius: 8px;
    background: #007FAE;
    color: #fff;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    margin-top: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: background 0.2s;
}

button:hover {
    background: #046588;
}

button:active {
    background: #007FAE;
}

button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.password-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

.password-container input[type="password"],
.password-container input[type="text"] {
    padding-right: 48px;
    width: 100%;
    box-sizing: border-box;
}

.toggle-password {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.5;
    pointer-events: auto;
}

/* Alert Messages */
/* Alert Messages */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    text-align: left;
}

.alert-error {
    background: rgba(220, 53, 69, 0.9);
    color: #fff;
    border: 1px solid #dc3545;
}

.alert-success {
    background: rgba(40, 167, 69, 0.9);
    color: #fff;
    border: 1px solid #28a745;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.9);
    color: #212529;
    border: 1px solid #ffc107;
}

.alert-info {
    background: rgba(23, 162, 184, 0.9);
    color: #fff;
    border: 1px solid #17a2b8;
}

.message-text {
    font-size: 0.9rem;
}

/* Info Text */
.info-text {
    color: #fff;
    font-size: 1rem;
    margin-bottom: 20px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Links */
.forgot-password {
    text-align: right;
    margin-top: -8px;
    margin-bottom: 8px;
}

.forgot-password a,
.back-to-login a,
.back-to-app a,
.register-link a {
    color: #fff;
    text-decoration: none;
    font-size: 0.9rem;
    opacity: 0.9;
    transition: opacity 0.2s;
}

.forgot-password a:hover,
.back-to-login a:hover,
.back-to-app a:hover,
.register-link a:hover {
    opacity: 1;
    text-decoration: underline;
}

.back-to-login,
.back-to-app {
    margin-top: 20px;
    text-align: center;
}

.register-link {
    margin-top: 16px;
    color: #fff;
    font-size: 0.9rem;
}

/* Password Requirements */
.password-requirements {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    text-align: left;
    font-size: 0.85rem;
}

.requirement {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #666;
}

.requirement:last-child {
    margin-bottom: 0;
}

.requirement .check {
    color: #28a745;
    font-weight: bold;
    margin-right: 8px;
    display: none;
}

.requirement .cross {
    color: #dc3545;
    font-weight: bold;
    margin-right: 8px;
    display: inline;
}

.requirement.valid .check {
    display: inline;
}

.requirement.valid .cross {
    display: none;
}

.requirement.valid {
    color: #28a745;
}

/* Info Message */
.info-message {
    text-align: center;
}

.info-message .message-text {
    color: #fff;
    font-size: 1.1rem;
    margin-bottom: 24px;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.action-button {
    margin: 20px 0;
}

.btn-primary {
    display: inline-block;
    padding: 12px 24px;
    background: #007FAE;
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    font-weight: bold;
    transition: background 0.2s;
}

.btn-primary:hover {
    background: #046588;
}

/* Responsive Design */
@media (max-width: 480px) {
    .center-content {
        max-width: 95vw;
        padding: 0 10px;
    }
    
    .logo {
        top: 16px;
        left: 16px;
        right: 16px;
    }
    
    .title-wrapper h2 {
        font-size: 1.8rem;
    }
    
    .logo h1 {
        font-size: 1.5em;
    }
}
