document.addEventListener('DOMContentLoaded', function() {
    // Password toggle functionality
    const toggleButtons = document.querySelectorAll('.toggle-password');
    
    toggleButtons.forEach(function(toggleButton) {
        const passwordInput = toggleButton.closest('.password-container').querySelector('input');
        const eyeOpen = toggleButton.getAttribute('data-eye-open');
        const eyeClosed = toggleButton.getAttribute('data-eye-closed');
        
        toggleButton.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleButton.src = eyeOpen;
            } else {
                passwordInput.type = 'password';
                toggleButton.src = eyeClosed;
            }
        });
    });
    
    // Password update form validation
    const passwordUpdateForm = document.getElementById('kc-passwd-update-form');
    if (passwordUpdateForm) {
        const newPasswordInput = document.getElementById('password-new');
        const confirmPasswordInput = document.getElementById('password-confirm');
        const updateButton = document.getElementById('update-password-btn');
        
        const requirements = {
            length: document.getElementById('length-req'),
            lowercase: document.getElementById('lowercase-req'),
            uppercase: document.getElementById('uppercase-req'),
            number: document.getElementById('number-req'),
            special: document.getElementById('special-req')
        };
        
        function validatePassword() {
            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            // Check length
            const hasLength = password.length >= 8;
            requirements.length.classList.toggle('valid', hasLength);
            
            // Check lowercase
            const hasLowercase = /[a-z]/.test(password);
            requirements.lowercase.classList.toggle('valid', hasLowercase);
            
            // Check uppercase
            const hasUppercase = /[A-Z]/.test(password);
            requirements.uppercase.classList.toggle('valid', hasUppercase);
            
            // Check number
            const hasNumber = /\d/.test(password);
            requirements.number.classList.toggle('valid', hasNumber);
            
            // Check special character
            const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
            requirements.special.classList.toggle('valid', hasSpecial);
            
            // Check if passwords match
            const passwordsMatch = password === confirmPassword && password.length > 0;
            
            // Enable button if all requirements are met
            const allValid = hasLength && hasLowercase && hasUppercase && hasNumber && hasSpecial && passwordsMatch;
            updateButton.disabled = !allValid;
        }
        
        newPasswordInput.addEventListener('input', validatePassword);
        confirmPasswordInput.addEventListener('input', validatePassword);
    }
});
