<#import "template.ftl" as layout>
<@layout.registrationLayout displayInfo=true; section, displayMessage, message, displayInfo>
    <#if section = "header">
        ${msg("updatePasswordTitle")}
    <#elseif section = "form">
    <div class="kc-form-reset">
        <form id="kc-passwd-update-form" class="${properties.kcFormClass!}" action="${url.loginAction}" method="post">
            <input type="text" id="username" name="username" value="${username}" autocomplete="username" readonly="readonly" style="display:none;"/>
            <input type="password" id="password" name="password" autocomplete="current-password" style="display:none;"/>

            <div id="kc-info" class="${properties.kcSignUpClass!}">
                <div id="kc-info-wrapper" class="${properties.kcInfoAreaWrapperClass!}">
                    ${msg("resetPasswordHeader")}
                </div>
            </div>

            <div class="${properties.kcFormGroupClass!}">
                <div class="${properties.kcInputWrapperClass!}">
                    <input type="password" id="password-new" name="password-new" class="${properties.kcInputClass!}" autofocus autocomplete="new-password" placeholder="${msg("passwordNew")}" />
                </div>
            </div>

            <div class="${properties.kcFormGroupClass!}">
                <div class="${properties.kcInputWrapperClass!}">
                    <input type="password" id="password-confirm" name="password-confirm" class="${properties.kcInputClass!}" autocomplete="new-password" placeholder="${msg("passwordConfirm")}" />
                </div>
            </div>

            <#if displayMessage && message?has_content && message.type = "error">
                <div class="alert alert-${message.type}">
                    <span class="kc-feedback-text">${kcSanitize(message.summary)?no_esc}</span>
                </div>
            <#else>
                <div class="login-button-spacer"></div>  
            </#if>

            <div class="${properties.kcFormGroupClass!}">
                <div id="kc-form-options" class="${properties.kcFormOptionsClass!}">
                    <div class="${properties.kcFormOptionsWrapperClass!}">
                    </div>
                </div>

                <div id="kc-form-buttons" class="${properties.kcFormButtonsClass!}">
                    <input class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!}" type="submit" value="${msg("doSubmit")}"/>
                </div>
            </div>
        </form>
        </div>
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            //Password validation
            var passwordNew = document.getElementById("password-new");
            var passwordConfirm = document.getElementById("password-confirm");
            var form = document.getElementById("kc-passwd-update-form");
            var isSubmitAllowed = true;
            var allRegex = {
                lowercase: {
                    reg: new RegExp("(?=.*[a-z])"),
                    msg: "1 lowercase alphabetical character"
                },
                uppercase: {
                    reg: /[A-Z]/,
                    msg: "1 uppercase alphabetical character"
                },
                numeric: {
                    reg: new RegExp("(?=.*[0-9])"),
                    msg: "1 numeric character"
                },
                specialCharacter: {
                    reg: /[!@#$%£^&*(),.?":{}|<>]/,
                    msg: "1 special character"
                },
                minNumOfCharacters: {
                    reg: new RegExp("(?=.{8,})"),
                    msg: "a minimum of 8 characters"
                }
            };
            
            passwordNew.addEventListener('input',function(event) {
                var password = passwordNew.value;
                var ul = document.createElement('ul');
                ul.style.cssText = "text-align: left";
                var isPasswordValid = true;

                for (var n in allRegex){
                    var li = document.createElement('li');
                    li.style.fontSize = "14px"
                    li.innerHTML = allRegex[n].msg;
                    if (allRegex[n].reg.test(password)) {
                        li.style.textDecoration = "line-through";
                    } else 
                    {
                        isPasswordValid = false;     
                    }
                    ul.appendChild(li);
                 }

                if (isPasswordValid) {
                    isSubmitAllowed = true;
                    simulateBackendError.showAlertDiv('Password must containt at least:<br>' + ul.outerHTML, 'alert-success');
                } else 
                {
                    isSubmitAllowed = true;
                    simulateBackendError.showAlertDiv('Password must containt at least:<br>' + ul.outerHTML, 'alert-warning');
                }
            });

            form.addEventListener('submit',function(event) {
                if(isSubmitAllowed) {
                    return true;
                } else 
                {
                    event.preventDefault(); 
                }                    
            });

        });
        </script>
    </#if>
</@layout.registrationLayout>
