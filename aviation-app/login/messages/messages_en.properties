doLogIn=Login
doRegister=Register
doCancel=Cancel
doSubmit=Submit
senLink=Send Link
doReset=Reset
doYes=Yes
doNo=No
doContinue=Continue
doIgnore=Ignore
doAccept=Accept
doDecline=Decline
loginInputPlaceholder=Email
passwordInputPlaceholder=Password
doForgotPassword=Forgotten your password?
doClickHere=Click here
doImpersonate=Impersonate
kerberosNotConfigured=Kerberos Not Configured
kerberosNotConfiguredTitle=Kerberos Not Configured
bypassKerberosDetail=Either you are not logged in via Kerberos or your browser is not set up for Kerberos login.  Please click continue to login in through other means
kerberosNotSetUp=Kerberos is not set up.  You cannot login.
registerTitle=Register
loginTitle=Login | Quest
loginTitleHtml={0}
impersonateTitle={0} Impersonate User
impersonateTitleHtml=<strong>{0}</strong> Impersonate User
realmChoice=Realm
unknownUser=Unknown user
loginTotpTitle=Mobile Authenticator Setup
loginProfileTitle=Update Account Information
loginTimeout=You took too long to login.  Login process starting from beginning.
oauthGrantTitle=Grant Access to {0}
oauthGrantTitleHtml={0}
errorTitle=We''re sorry...
errorTitleHtml=We''re <strong>sorry</strong> ...
emailVerifyTitle=Email verification
emailForgotTitle=Forgot Your Password?
updatePasswordTitle=Update password
codeSuccessTitle=Success code
codeErrorTitle=Error code\: {0}
displayUnsupported=Requested display type unsupported
browserRequired=Browser required to login
browserContinue=Browser required to complete login
browserContinuePrompt=Open browser and continue login? [y/n]:
browserContinueAnswer=y


termsTitle=Terms and Conditions
termsText=<p>Terms and conditions to be defined</p>
termsPlainText=Terms and conditions to be defined.

recaptchaFailed=Invalid Recaptcha
recaptchaNotConfigured=Recaptcha is required, but not configured
consentDenied=Consent denied.

noAccount=New user?
username=Username
usernameOrEmail=Username or email
firstName=First name
givenName=Given name
fullName=Full name
lastName=Last name
familyName=Family name
email=Email
password=Password
passwordConfirm=Confirm password
passwordNew=New Password
passwordNewConfirm=Confirm password
resetPasswordHeader=Choose a new password to reactivate your Quest account.
rememberMe=Remember me
authenticatorCode=One-time code
address=Address
street=Street
locality=City or Locality
region=State, Province, or Region
postal_code=Zip or Postal code
country=Country
emailVerified=Email verified
gssDelegationCredential=GSS Delegation Credential

profileScopeConsentText=User profile
emailScopeConsentText=Email address
addressScopeConsentText=Address
phoneScopeConsentText=Phone number
offlineAccessScopeConsentText=Offline Access
samlRoleListScopeConsentText=My Roles
rolesScopeConsentText=User roles

loginTotpIntro=You are required to set up a One Time Password generator to access this account
loginTotpStep1=Install one of the following applications on your mobile
loginTotpStep2=Open the application and scan the barcode
loginTotpStep3=Enter the one-time password provided by the application and click 'Submit' to finish the setup
loginTotpManualStep2=Open the application and enter the key
loginTotpManualStep3=Use the following configuration values if the application allows setting them
loginTotpUnableToScan=Unable to scan?
loginTotpScanBarcode=Scan barcode?
loginTotpOneTime=One-time code
loginTotpType=Type
loginTotpAlgorithm=Algorithm
loginTotpDigits=Digits
loginTotpInterval=Interval
loginTotpCounter=Counter

loginTotp.totp=Time-based
loginTotp.hotp=Counter-based


oauthGrantRequest=Do you grant these access privileges?
inResource=in

emailVerifyInstruction1=An email with instructions to verify your email address has been sent to you.
emailVerifyInstruction2=Haven''t received a verification code in your email?
emailVerifyInstruction3=to re-send the email.

emailLinkIdpTitle=Link {0}
emailLinkIdp1=Verification email
emailLinkIdp2=An email should have arrived in your inbox. If you haven''t recieved the verification email
emailLinkIdp3=to resend.

backToLogin=Back to Login

emailInstruction=Enter your email address and we''ll send you a link via email to reset your password.

copyCodeInstruction=Please copy this code and paste it into your application:

pageExpiredTitle=Page has expired
pageExpiredMsg1=To restart the login process
pageExpiredMsg2=To continue the login process

personalInfo=Personal Info:
role_admin=Admin
role_realm-admin=Realm Admin
role_create-realm=Create realm
role_create-client=Create client
role_view-realm=View realm
role_view-users=View users
role_view-applications=View applications
role_view-clients=View clients
role_view-events=View events
role_view-identity-providers=View identity providers
role_manage-realm=Manage realm
role_manage-users=Manage users
role_manage-applications=Manage applications
role_manage-identity-providers=Manage identity providers
role_manage-clients=Manage clients
role_manage-events=Manage events
role_view-profile=View profile
role_manage-account=Manage account
role_manage-account-links=Manage account links
role_read-token=Read token
role_offline-access=Offline access
client_account=Account
client_security-admin-console=Security Admin Console
client_admin-cli=Admin CLI
client_realm-management=Realm Management
client_broker=Broker

invalidUserMessage=Invalid email or password. After 5 failed attempts your account will be locked.
invalidEmailMessage=Invalid email address.
accountDisabledMessage=This account has been locked for your protection.
lockedAccountMessageHeader=This account has been locked for your protection.
lockedAccountMessageContent=<NAME_EMAIL> for account re-activation.
blockedAccountMessageHeader=This account has been locked for your protection.
blockedAccountMessageContent=<NAME_EMAIL> for account re-activation.
mailSentMessageHeader=Please check your inbox
mailSentMessageContent=We''ve sent a password reset link to {0}. If you do not get the email in a few minutes, please check whether it got into the spam folder and make sure it was the email address you are registered with.
accountTemporarilyDisabledMessage=Account is temporarily disabled. Contact admin or try again later.
expiredCodeMessage=Login timeout. Please login again.
expiredActionMessage=Action expired. Please continue with login now.
expiredActionTokenNoSessionMessage=Your password reset link has expired.
expiredActionTokenSessionExistsMessage=Action expired. Please start again.

missingFirstNameMessage=Please specify first name.
missingLastNameMessage=Please specify last name.
missingEmailMessage=Please specify email.
missingUsernameMessage=Please specify username.
missingPasswordMessage=Please specify password.
missingTotpMessage=Please specify authenticator code.
notMatchPasswordMessage=Passwords do not match

invalidPasswordExistingMessage=Invalid existing password.
invalidPasswordBlacklistedMessage=Invalid password: password is blacklisted.
invalidPasswordConfirmMessage=Password confirmation doesn''t match.
invalidTotpMessage=Invalid authenticator code.

usernameExistsMessage=Username already exists.
emailExistsMessage=Email already exists.

federatedIdentityExistsMessage=User with {0} {1} already exists. Please login to account management to link the account.

confirmLinkIdpTitle=Account already exists
federatedIdentityConfirmLinkMessage=User with {0} {1} already exists. How do you want to continue?
federatedIdentityConfirmReauthenticateMessage=Authenticate as {0} to link your account with {1}
confirmLinkIdpReviewProfile=Review profile
confirmLinkIdpContinue=Send verification email

configureTotpMessage=You need to set up Mobile Authenticator to activate your account.
updateProfileMessage=You need to update your user profile to activate your account.
updatePasswordMessage=You need to change your password to activate your account.
resetPasswordMessage=You need to change your password.
verifyEmailMessage=You need to verify your email address to activate your account.
linkIdpMessage=You need to verify your email address to link your account with {0}.

emailSentMessage=You should receive an email shortly with further instructions.
emailSendErrorMessage=Failed to send email, please try again later.

accountUpdatedMessage=Your password has been reset
accountPasswordUpdatedMessage=Your password has been updated.

delegationCompleteHeader=Login Successful
delegationCompleteMessage=You may close this browser window and go back to your console application.
delegationFailedHeader=Login Failed
delegationFailedMessage=You may close this browser window and go back to your console application and try logging in again.

noAccessMessage=No access

invalidPasswordMinLengthMessage=Invalid password: minimum length {0}.
invalidPasswordMinDigitsMessage=Invalid password: must contain at least {0} numerical digits.
invalidPasswordMinLowerCaseCharsMessage=Invalid password: must contain at least {0} lower case characters.
invalidPasswordMinUpperCaseCharsMessage=Invalid password: must contain at least {0} upper case characters.
invalidPasswordMinSpecialCharsMessage=Invalid password: must contain at least {0} special characters.
invalidPasswordNotUsernameMessage=Invalid password: must not be equal to the username.
invalidPasswordRegexPatternMessage=Invalid password: fails to match regex pattern(s).
invalidPasswordHistoryMessage=Invalid password: must not be equal to any of last {0} passwords.
invalidPasswordGenericMessage=Invalid password: new password doesn''t match password policies.

failedToProcessResponseMessage=Failed to process response
httpsRequiredMessage=HTTPS required
realmNotEnabledMessage=Realm not enabled
invalidRequestMessage=Invalid Request
failedLogout=Logout failed
unknownLoginRequesterMessage=Unknown login requester
loginRequesterNotEnabledMessage=Login requester not enabled
bearerOnlyMessage=Bearer-only applications are not allowed to initiate browser login
standardFlowDisabledMessage=Client is not allowed to initiate browser login with given response_type. Standard flow is disabled for the client.
implicitFlowDisabledMessage=Client is not allowed to initiate browser login with given response_type. Implicit flow is disabled for the client.
invalidRedirectUriMessage=Invalid redirect uri
unsupportedNameIdFormatMessage=Unsupported NameIDFormat
invalidRequesterMessage=Invalid requester
registrationNotAllowedMessage=Registration not allowed
resetCredentialNotAllowedMessage=Reset Credential not allowed

permissionNotApprovedMessage=Permission not approved.
noRelayStateInResponseMessage=No relay state in response from identity provider.
insufficientPermissionMessage=Insufficient permissions to link identities.
couldNotProceedWithAuthenticationRequestMessage=Could not proceed with authentication request to identity provider.
couldNotObtainTokenMessage=Could not obtain token from identity provider.
unexpectedErrorRetrievingTokenMessage=Unexpected error when retrieving token from identity provider.
unexpectedErrorHandlingResponseMessage=Unexpected error when handling response from identity provider.
identityProviderAuthenticationFailedMessage=Authentication failed. Could not authenticate with identity provider.
identityProviderDifferentUserMessage=Authenticated as {0}, but expected to be authenticated as {1}
couldNotSendAuthenticationRequestMessage=Could not send authentication request to identity provider.
unexpectedErrorHandlingRequestMessage=Unexpected error when handling authentication request to identity provider.
invalidAccessCodeMessage=Invalid access code.
sessionNotActiveMessage=Session not active.
invalidCodeMessage=An error occurred, please login again through your application.
identityProviderUnexpectedErrorMessage=Unexpected error when authenticating with identity provider
identityProviderNotFoundMessage=Could not find an identity provider with the identifier.
identityProviderLinkSuccess=You successfully verified your email. Please go back to your original browser and continue there with the login.
staleCodeMessage=This page is no longer valid, please go back to your application and login again
realmSupportsNoCredentialsMessage=Realm does not support any credential type.
identityProviderNotUniqueMessage=Realm supports multiple identity providers. Could not determine which identity provider should be used to authenticate with.
emailVerifiedMessage=Your email address has been verified.
staleEmailVerificationLink=The link you clicked is an old stale link and is no longer valid.  Maybe you have already verified your email?
identityProviderAlreadyLinkedMessage=Federated identity returned by {0} is already linked to another user.
confirmAccountLinking=Confirm linking the account {0} of identity provider {1} with your account.
confirmEmailAddressVerification=Confirm validity of e-mail address {0}.
confirmExecutionOfActions=Required action(s)

locale_ca=Catal\u00E0
locale_de=Deutsch
locale_en=English
locale_es=Espa\u00F1ol
locale_fr=Fran\u00e7ais
locale_it=Italiano
locale_ja=\u65E5\u672C\u8A9E
locale_nl=Nederlands
locale_no=Norsk
locale_pl=Polish
locale_pt_BR=Portugu\u00EAs (Brasil)
locale_pt-BR=Portugu\u00EAs (Brasil)
locale_ru=\u0420\u0443\u0441\u0441\u043A\u0438\u0439
locale_lt=Lietuvi\u0173
locale_zh-CN=\u4e2d\u6587\u7b80\u4f53
locale_sk=Sloven\u010Dina
locale_sv=Svenska

backToApplication=Back to Application
missingParameterMessage=Missing parameters\: {0}
clientNotFoundMessage=Client not found.
clientDisabledMessage=Client disabled.
invalidParameterMessage=Invalid parameter\: {0}
alreadyLoggedIn=You are already logged in.
differentUserAuthenticated=You are already authenticated as different user ''{0}'' in this session. Please logout first.
brokerLinkingSessionExpired=Requested broker account linking, but current session is no longer valid.
proceedWithAction=Click here to proceed

requiredAction.CONFIGURE_TOTP=Configure OTP
requiredAction.terms_and_conditions=Terms and Conditions
requiredAction.UPDATE_PASSWORD=Update Password
requiredAction.UPDATE_PROFILE=Update Profile
requiredAction.VERIFY_EMAIL=Verify Email

p3pPolicy=CP="This is not a P3P policy!"

doX509Login=You will be logged in as\:
clientCertificate=X509 client certificate\:
noCertificate=[No Certificate]


pageNotFound=Page not found
internalServerError=An internal server error has occurred

console-username=Username:
console-password=Password:
console-otp=One Time Password:
console-new-password=New Password:
console-confirm-password=Confirm Password:
console-update-password=Update of your password is required.
console-verify-email=You are required to verify your email address.  An email has been sent to {0} that contains a verification code.  Please enter this code into the input below.
console-email-code=Email Code:
console-accept-terms=Accept Terms? [y/n]:
console-accept=y

verify-your-account=Verify your account
account-verification-description=We need to verify your account, please click the button below to send an email with a verification link.
