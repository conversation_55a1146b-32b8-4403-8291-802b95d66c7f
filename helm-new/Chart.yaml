apiVersion: v2
name: keycloak
version: 18.1.1
appVersion: 17.0.1-legacy
description: Open Source Identity and Access Management For Modern Applications and Services
keywords:
  - sso
  - idm
  - openid connect
  - saml
  - kerberos
  - ldap
home: https://www.keycloak.org/
icon: https://www.keycloak.org/resources/images/keycloak_icon_512px.svg
sources:
  - https://github.com/codecentric/helm-charts
  - https://github.com/jboss-dockerfiles/keycloak
  - https://github.com/bitnami/charts/tree/master/bitnami/postgresql
maintainers:
  - name: unguiculus
    email: <EMAIL>
  - name: thomasdarimont
    email: <EMAIL>
# dependencies:
#   - name: postgresql
#     version: 10.3.13
#     repository: https://charts.bitnami.com/bitnami
#     condition: postgresql.enabled
