extraEnv: |
  - name: DB_VENDOR
    value: h2
  - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_USER_FILE
    value: /secrets/admin-creds/user
  - name: KE<PERSON><PERSON>OAK_PASSWORD_FILE
    value: /secrets/admin-creds/password
  - name: JAVA_OPTS
    value: >-
      -XX:+UseContainerSupport
      -XX:MaxRAMPercentage=50.0
      -Djava.net.preferIPv4Stack=true
      -Djboss.modules.system.pkgs=$JBOSS_MODULES_SYSTEM_PKGS
      -Djava.awt.headless=true

secrets:
  admin-creds:
    annotations:
      my-test-annotation: Test secret for {{ include "keycloak.fullname" . }}
    stringData:
      user: admin
      password: secret

extraVolumeMounts: |
  - name: admin-creds
    mountPath: /secrets/admin-creds
    readOnly: true

extraVolumes: |
  - name: admin-creds
    secret:
      secretName: '{{ include "keycloak.fullname" . }}-admin-creds'

postgresql:
  enabled: false


ingress:
  enabled: true
  annotations:
    test-annotation1: annotationValue1
    test-annotation2: annotationValue2
  labels:
    test-label1: labelValue1
    test-label2: labelValue2
  # List of rules for the Ingress
  rules:
    -
      # Ingress host
      host: '{{ .Release.Name }}.keycloak.example.com'
      # Paths for the host
      paths:
        - path: /
          pathType: Prefix
  # TLS configuration

  console:
    enabled: true
    annotations:
      test-annotation1: annotationValue1
      test-annotation2: annotationValue2
    labels:
      test-label1: labelValue1
      test-label2: labelValue2
    ingressClassName: ""
    rules:
      -
        host: '{{ .Release.Name }}.keycloak.example.com'
        # Paths for the host
        paths:
          - path: /auth/admin/
            pathType: Prefix

test:
  enabled: true
