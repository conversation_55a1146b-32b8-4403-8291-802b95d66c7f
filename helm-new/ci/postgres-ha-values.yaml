replicas: 2

podLabels:
  test-label: test-label-value

podAnnotations:
  test-annotation: test-annotation-value-{{ .Release.Name }}
  test-int-annotation: "12345"

startupScripts:
  hello.sh: |
    #!/bin/sh

    echo '********************************************************************************'
    echo '*                                                                              *'
    echo '*                        Hello from my startup script!                         *'
    echo '*                                                                              *'
    echo '********************************************************************************'

lifecycleHooks: |
  postStart:
    exec:
      command:
        - /bin/sh
        - -c
        - echo 'Hello from lifecycle hook!'

extraEnv: |
  - name: <PERSON><PERSON><PERSON><PERSON><PERSON>_DISCOVERY_PROTOCOL
    value: dns.DNS_PING
  - name: J<PERSON><PERSON><PERSON>S_DISCOVERY_PROPERTIES
    value: 'dns_query={{ include "keycloak.serviceDnsName" . }}'
  - name: CACHE_OWNERS_COUNT
    value: "2"
  - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
    value: "2"
  - name: KEYCLOAK_USER_FILE
    value: /secrets/admin-creds/user
  - name: KEYCLOAK_PASSWORD_FILE
    value: /secrets/admin-creds/password
  - name: KEYCLOAK_STATISTICS
    value: all
  - name: JAVA_OPTS
    value: >-
      -XX:+UseContainerSupport
      -XX:MaxRAMPercentage=50.0
      -Djava.net.preferIPv4Stack=true
      -Djboss.modules.system.pkgs=$JBOSS_MODULES_SYSTEM_PKGS
      -Djava.awt.headless=true

secrets:
  admin-creds:
    stringData:
      user: admin
      password: secret

extraVolumeMounts: |
  - name: admin-creds
    mountPath: /secrets/admin-creds
    readOnly: true

extraVolumes: |
  - name: admin-creds
    secret:
      secretName: '{{ include "keycloak.fullname" . }}-admin-creds'

postgresql:
  enabled: true
  persistence:
    enabled: true

test:
  enabled: true
