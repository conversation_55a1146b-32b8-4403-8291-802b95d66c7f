{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.autoscaling.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: {{ include "keycloak.fullname" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics: 
    {{- toYaml .Values.autoscaling.metrics | nindent 4 }}
  behavior:
    {{- toYaml .Values.autoscaling.behavior | nindent 4 }}
{{- end }}
