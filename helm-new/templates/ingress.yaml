{{- $ingress := .Values.ingress -}}
{{- if $ingress.enabled -}}
{{- $apiV1 := false -}}
{{- $apiVersion := "extensions/v1beta1" -}}
{{- if and (.Capabilities.APIVersions.Has "networking.k8s.io/v1") (semverCompare ">= v1.19.0-0" .Capabilities.KubeVersion.Version) -}}
  {{- $apiVersion = "networking.k8s.io/v1" -}}
  {{- $apiV1 = true -}}
{{- else if .Capabilities.APIVersions.Has "networking.k8s.io/v1beta1" -}}
  {{- $apiVersion = "networking.k8s.io/v1beta1" -}}
{{- else -}}
  {{- $apiVersion = "extensions/v1beta1" -}}
{{- end }}
apiVersion: {{ $apiVersion }}
kind: Ingress
metadata:
  name: {{ include "keycloak.fullname" . }}
  {{- with $ingress.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := $ingress.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
{{- if $ingress.ingressClassName }}
  ingressClassName: {{ $ingress.ingressClassName }}
{{- end }}
{{- if $ingress.tls }}
  tls:
    {{- range $ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ tpl . $ | quote }}
      {{- end }}
      {{- with .secretName }}
      secretName: {{ tpl . $ }}
      {{- end }}
    {{- end }}
{{- end }}
  rules:
    {{- range .Values.ingress.rules }}
    - 
      {{- if .host }}
      host: {{ tpl .host $ | quote }}
      {{- end }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if $apiV1 }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ include "keycloak.fullname" $ }}-http
                port:
                  name: {{ $ingress.servicePort }}
            {{- else }}
            backend:
              serviceName: {{ include "keycloak.fullname" $ }}-http
              servicePort: {{ $ingress.servicePort }}
            {{- end }}
          {{- end }}
    {{- end }}
{{- if $ingress.console.enabled }}
---
apiVersion: {{ $apiVersion }}
kind: Ingress
metadata:
  name: {{ include "keycloak.fullname" . }}-console
  {{- with $ingress.console.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := $ingress.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
{{- if $ingress.console.ingressClassName }}
  ingressClassName: {{ $ingress.console.ingressClassName }}
{{- end }}
{{- if $ingress.console.tls }}
  tls:
    {{- range $ingress.console.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ tpl . $ | quote }}
      {{- end }}
      {{- with .secretName }}
      secretName: {{ tpl . $ }}
      {{- end }}
    {{- end }}
{{ else if $ingress.tls }}
  tls:
    {{- range $ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ tpl . $ | quote }}
      {{- end }}
      {{- with .secretName }}
      secretName: {{ tpl . $ }}
      {{- end }}
    {{- end }}
{{- end }}
  rules:
    {{- range .Values.ingress.console.rules }}
    - 
      {{- if .host }}
      host: {{ tpl .host $ | quote }}
      {{- end }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            {{- if $apiV1 }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ include "keycloak.fullname" $ }}-http
                port:
                  name: {{ $ingress.servicePort }}
            {{- else }}
            backend:
              serviceName: {{ include "keycloak.fullname" $ }}-http
              servicePort: {{ $ingress.servicePort }}
            {{- end }}
          {{- end }}
    {{- end }}
{{- end -}}
{{- end -}}
