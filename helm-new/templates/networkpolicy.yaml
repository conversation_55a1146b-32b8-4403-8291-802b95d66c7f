{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "keycloak.fullname" . | quote }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.networkPolicy.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
  policyTypes:
    - Ingress
  podSelector:
    matchLabels:
      {{- include "keycloak.selectorLabels" . | nindent 6 }}
  ingress:
    {{- with .Values.networkPolicy.extraFrom }}
    - from:
        {{- toYaml . | nindent 8 }}
      ports:
        - protocol: TCP
          port: 8080
        - protocol: TCP
          port: 8443
        {{ range $.Values.extraPorts }}
        - protocol: {{ default "TCP" .protocol }}
          port: {{ .containerPort }}
        {{- end }}
    {{- end }}
    - from:
        - podSelector:
            matchLabels:
              {{- include "keycloak.selectorLabels" . | nindent 14 }}
      ports:
        - protocol: TCP
          port: 8080
        - protocol: TCP
          port: 8443
        - protocol: TCP
          port: 9990
        {{ range .Values.extraPorts }}
        - protocol: {{ default "TCP" .protocol }}
          port: {{ .containerPort }}
        {{- end }}
{{- end }}
