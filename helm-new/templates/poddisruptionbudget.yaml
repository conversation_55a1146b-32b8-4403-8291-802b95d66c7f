{{- if .Values.podDisruptionBudget -}}
{{- if semverCompare ">=1.21.0" .Capabilities.KubeVersion.GitVersion }}
apiVersion: policy/v1
{{- else }}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" . | nindent 6 }}
  {{- toYaml .Values.podDisruptionBudget | nindent 2 }}
{{- end -}}
