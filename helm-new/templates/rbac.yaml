{{- if and .Values.rbac.create .Values.rbac.rules }}
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
rules:
  {{- toYaml .Values.rbac.rules | nindent 2 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "keycloak.fullname" . }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "keycloak.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak.serviceAccountName" . }}
    namespace: {{ .Release.Namespace | quote }}
{{- end }}
