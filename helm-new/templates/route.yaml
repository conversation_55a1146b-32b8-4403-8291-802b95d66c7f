{{- $route := .Values.route -}}
{{- if $route.enabled -}}
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: {{ include "keycloak.fullname" . }}
  {{- with $route.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := $route.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
spec:
{{- if $route.host }}
  host: {{ tpl $route.host $ | quote }}
{{- end }}
  path: {{ $route.path }}
  port:
    {{- if or (not $route.tls.enabled) (eq $route.tls.termination "edge") }}
    targetPort: http
    {{- else}}
    targetPort: https
    {{- end}}
  to:
    kind: Service
    name: {{ include "keycloak.fullname" $ }}-http
    weight: 100
  {{- if $route.tls.enabled }}
  tls:
    insecureEdgeTerminationPolicy: {{ $route.tls.insecureEdgeTerminationPolicy }}
    termination: {{ $route.tls.termination }}
  {{- end }}
{{- end -}}
