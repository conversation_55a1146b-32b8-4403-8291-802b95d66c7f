{{- $highAvailability := gt (int .Values.replicas) 1 -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "keycloak.fullname" . }}-headless
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    app.kubernetes.io/component: headless
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: http
      port: {{ .Values.service.httpPort }}
      targetPort: http
      protocol: TCP
  selector:
    {{- include "keycloak.selectorLabels" . | nindent 4 }}
