{{- if .Values.test.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "keycloak.fullname" . }}-test
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
  annotations:
    helm.sh/hook: test
    helm.sh/hook-delete-policy: hook-succeeded
data:
  test.py: |
    import os
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions
    from urllib.parse import urlparse

    print('Creating PhantomJS driver...')
    driver = webdriver.PhantomJS(service_log_path='/tmp/ghostdriver.log')

    base_url = 'http://{{ include "keycloak.fullname" . }}-http{{ if ne 80 (int .Values.service.httpPort) }}:{{ .Values.service.httpPort }}{{ end }}'

    print('Opening Keycloak...')
    driver.get('{0}/auth/admin/'.format(base_url))

    username = os.environ['KEYCLOAK_USER']
    password = os.environ['KEYCLOAK_PASSWORD']

    username_input = WebDriverWait(driver, 30).until(expected_conditions.presence_of_element_located((By.ID, "username")))
    password_input = WebDriverWait(driver, 30).until(expected_conditions.presence_of_element_located((By.ID, "password")))
    login_button = WebDriverWait(driver, 30).until(expected_conditions.presence_of_element_located((By.ID, "kc-login")))

    print('Entering username...')
    username_input.send_keys(username)

    print('Entering password...')
    password_input.send_keys(password)

    print('Clicking login button...')
    login_button.click()

    WebDriverWait(driver, 30).until(lambda driver: '/auth/admin/master/console/' in driver.current_url)

    print('Admin console visible. Login successful.')

    driver.quit()

  {{- end }}
