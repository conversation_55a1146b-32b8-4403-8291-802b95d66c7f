image:
  repository: quay.io/keycloak/keycloak
  tag: 19.0
postgresql:
  # Disable PostgreSQL dependency
  enabled: false
replicas: 2

# keycloak:
#   hostsname: gcp-dev-keycloak.questmarine.app

service:
  type: NodePort

# nodeSelector:
#   on-demand: "true"
#resources: 
#  requests:
#    cpu: "500m"
#    memory: "1024Mi"
#  limits:
#    cpu: "2000m"
#    memory: "4Gi"

extraEnv: |
  - name: DB_VENDOR
    value: mysql
  - name: DB_ADDR
    value: ***********
  - name: DB_PORT
    value: "3306"
  - name: DB_DATABASE
    value: keycloak_np
  - name: KEYCLOAK_USER
    value: admin
  - name: KE<PERSON><PERSON>OAK_PASSWORD
    value: gxFhUMRve3RqNC8w
  - name: DB_USER
    value: keyclock_np
  - name: DB_PASSWORD
    value: Xu/2mQBF)VHf8}UP
  - name: JDBC_PARAMS
    value: useSSL=false
  - name: JGROUPS_DISCOVERY_PROTOCOL
    value: dns.DNS_PING
  - name: J<PERSON><PERSON><PERSON>S_DISCOVERY_PROPERTIES
    value: 'dns_query={{ include "keycloak.serviceDnsName" . }}'
  - name: CACHE_OWNERS_COUNT
    value: "2"
  - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
    value: "2"
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"
  # - name: KEYCLOAK_FRONTEND_URL	
  #   value: https://eng-nonprod-keycloak-eu.concirrusquest.com/auth
  - name: KEYCLOAK_IMPORT
    value: /tmp/realm-export.json
  - name: GOOGLE_APPLICATION_CREDENTIALS
    value: /opt/jboss/keycloak/adc.json
  - name: GCP_PROJECT_ID
    value: prj-nonprod-eng-svc-01
  - name: PUBSUB_TOPIC
    value: hull-user-spi   
# tolerations:
#   - key: "auth-service"
#     operator: "Equal"
#     value: "true"
#     effect: "NoSchedule"


