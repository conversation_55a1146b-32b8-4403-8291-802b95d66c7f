image:
  repository: quay.io/keycloak/keycloak
  tag: 19.0
postgresql:
  # Disable PostgreSQL dependency
  enabled: false
replicas: 6

keycloak:
  hostsname: auth.questmarine.app

# nodeSelector:
#   on-demand: "true"
resources: 
  requests:
    cpu: "100m"
    memory: "500Mi"
  limits:
    cpu: "4000m"
    memory: "8Gi"

extraEnv: |
  - name: DB_VENDOR
    value: mysql
  - name: DB_ADDR
    value: ***********
  - name: DB_PORT
    value: "3306"
  - name: DB_DATABASE
    value: keycloak_prod
  - name: KEYCLOAK_USER
    value: admin
  - name: KEYCLOAK_PASSWORD
    value: nONiSKDJJwifgtAL
  - name: DB_USER
    value: keycloak_prod
  - name: DB_PASSWORD
    value: iEaZmjKUWGSBwBw7
  - name: JDBC_PARAMS
    value: useSSL=false
  - name: JGROUPS_DISCOVERY_PROTOCOL
    value: dns.DNS_PING
  - name: JGR<PERSON>UPS_DISCOVERY_PROPERTIES
    value: 'dns_query={{ include "keycloak.serviceDnsName" . }}'
  - name: CACHE_OWNERS_COUNT
    value: "2"
  - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
    value: "2"
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"
  - name: KEYCLOAK_FRONTEND_URL	
    value: https://auth.questmarine.app/auth
  - name: KEYCLOAK_IMPORT
    value: /tmp/realm-export.json
# tolerations:
#   - key: "account-service"
#     operator: "Equal"
#     value: "true"
#     effect: "NoSchedule"
# nodeSelector:
#   service: "account"


