image:
  repository: 400319975147.dkr.ecr.eu-west-2.amazonaws.com/nonprod-keycloak
  tag: 13.0.0-staging-legacy
postgresql:
  # Disable PostgreSQL dependency
  enabled: false
replicas: 2

keycloak:
  hostsname: staging-legacy-auth.questmarine.app

extraEnv: |
  - name: DB_VENDOR
    value: mysql
  - name: DB_ADDR
    value: ***********
  - name: DB_PORT
    value: "3306"
  - name: DB_DATABASE
    value: keycloak_staging_legacy
  - name: KEYCLOAK_USER
    value: admin
  - name: KEYCLOAK_PASSWORD
    value: gxFhUMRve3RqNC8w
  - name: DB_USER
    value: keyclock_np
  - name: DB_PASSWORD
    value: Xu/2mQBF)VHf8}UP
  - name: JDBC_PARAMS
    value: useSSL=false
  - name: JGROUPS_DISCOVERY_PROTOCOL
    value: dns.DNS_PING
  - name: JGR<PERSON>UPS_DISCOVERY_PROPERTIES
    value: 'dns_query={{ include "keycloak.serviceDnsName" . }}'
  - name: CACHE_OWNERS_COUNT
    value: "2"
  - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
    value: "2"
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"
  - name: KEYCLOAK_IMPORT
    value: /tmp/realm-export.json
  - name: KEYCLOAK_FRONTEND_URL
    value: https://staging-legacy-auth.questmarine.app/auth
  - name: GOOGLE_APPLICATION_CREDENTIALS
    value: /opt/jboss/keycloak/adc.json
  - name: GCP_PROJECT_ID
    value: prj-nonprod-eng-svc-01
  - name: PUBSUB_TOPIC
    value: hull-user-spi   



