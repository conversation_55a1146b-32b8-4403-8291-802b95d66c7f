extraEnv: |
  - name: DB_VENDOR
    value: h2
  - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_USER_FILE
    value: /secrets/admin-creds/user
  - name: KE<PERSON><PERSON><PERSON>K_PASSWORD_FILE
    value: /secrets/admin-creds/password
  - name: JAVA_OPTS
    value: >-
      -XX:+UseContainerSupport
      -XX:MaxRAMPercentage=50.0
      -Djava.net.preferIPv4Stack=true
      -Djboss.modules.system.pkgs=$JBOSS_MODULES_SYSTEM_PKGS
      -Djava.awt.headless=true

secrets:
  admin-creds:
    annotations:
      my-test-annotation: Test secret for {{ include "keycloak.fullname" . }}
    stringData:
      user: admin
      password: secret

extraVolumeMounts: |
  - name: admin-creds
    mountPath: /secrets/admin-creds
    readOnly: true

extraVolumes: |
  - name: admin-creds
    secret:
      secretName: '{{ include "keycloak.fullname" . }}-admin-creds'

postgresql:
  enabled: false

test:
  enabled: true
