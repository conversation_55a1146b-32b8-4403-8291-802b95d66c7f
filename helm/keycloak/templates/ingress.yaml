# apiVersion: extensions/v1beta1
# kind: Ingress
# metadata:
#   name: {{ .Release.Name }}
#   labels:
#     app: traefik
# spec:
#   rules:
#   - host: {{ .Values.keycloak.hostname }}
#     http:
#       paths:
#       - path: /
#         backend:
#           serviceName: {{ .Release.Name }}-http
#           servicePort: 80
kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  name: {{ .Release.Name }}
  labels:
    app: traefik
spec:
  rules:
    - host: {{ .Values.keycloak.hostname }}
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ .Release.Name }}-http
                port:
                  number: 80

