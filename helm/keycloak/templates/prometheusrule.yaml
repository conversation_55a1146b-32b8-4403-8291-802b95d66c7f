{{- with .Values.prometheusRule -}}
{{- if .enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ include "keycloak.fullname" $ }}
  {{- with .annotations }}
  annotations:
  {{- range $key, $value := . }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
  {{- end }}
  labels:
  {{- include "keycloak.labels" $ | nindent 4 }}
  {{- range $key, $value := .labels }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
spec:
  groups:
    - name: {{ include "keycloak.fullname" $ }}
      rules:
        {{- toYaml .rules | nindent 8 }}
{{- end }}
{{- end -}}
