{{- if and .Values.rbac.create .Values.rbac.rules }}
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ include "keycloak.fullname" . }}
rules:
  {{- toYaml .Values.rbac.rules | nindent 2 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "keycloak.fullname" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "keycloak.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "keycloak.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
