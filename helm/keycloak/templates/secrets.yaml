{{- range $nameSuffix, $values := .Values.secrets -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "keycloak.fullname" $ }}-{{ $nameSuffix }}
  {{- with $values.annotations }}
  annotations:
  {{- range $key, $value := . }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
  {{- end }}
  labels:
  {{- include "keycloak.labels" $ | nindent 4 }}
  {{- range $key, $value := $values.labels }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
type: Opaque
{{- with $values.data }}
data:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- with $values.stringData }}
stringData:
  {{- range $key, $value := . }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 2 }}
  {{- end }}
{{- end }}
---
{{- end -}}
