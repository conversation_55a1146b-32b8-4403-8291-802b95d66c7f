apiVersion: v1
kind: Service
metadata:
  name: {{ include "keycloak.fullname" . }}-http
  {{- with .Values.service.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.service.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
    app.kubernetes.io/component: http
spec:
  type: {{ .Values.service.type }}
  {{- if and (eq "LoadBalancer" .Values.service.type) .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.service.httpPort }}
      targetPort: http
      {{- if and (eq "NodePort" .Values.service.type) .Values.service.httpNodePort }}
      nodePort: {{ .Values.service.httpNodePort }}
      {{- end }}
      protocol: TCP
    - name: https
      port: {{ .Values.service.httpsPort }}
      targetPort: https
      {{- if and (eq "NodePort" .Values.service.type) .Values.service.httpsNodePort }}
      nodePort: {{ .Values.service.httpsNodePort }}
      {{- end }}
      protocol: TCP
    - name: http-management
      port: {{ .Values.service.httpManagementPort }}
      targetPort: http-management
      {{- if and (eq "NodePort" .Values.service.type) .Values.service.httpManagementNodePort }}
      nodePort: {{ .Values.service.httpManagementNodePort }}
      {{- end }}
      protocol: TCP
    {{- with .Values.service.extraPorts }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  selector:
    {{- include "keycloak.selectorLabels" . | nindent 4 }}
