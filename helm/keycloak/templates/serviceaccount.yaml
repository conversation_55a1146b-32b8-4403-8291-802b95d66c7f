{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "keycloak.serviceAccountName" . }}
  {{- with .Values.serviceAccount.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    {{- range $key, $value := .Values.serviceAccount.labels }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
imagePullSecrets:
  {{- toYaml .Values.serviceAccount.imagePullSecrets | nindent 4 }}
{{- end }}
