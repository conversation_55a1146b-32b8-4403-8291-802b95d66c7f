{{- with .Values.serviceMonitor -}}
{{- if .enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "keycloak.fullname" $ }}
  {{- with .namespace }}
  namespace: {{ . }}
  {{- end }}
  {{- with .annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
    {{- end }}
  {{- end }}
  labels:
  {{- include "keycloak.labels" $ | nindent 4 }}
  {{- range $key, $value := .labels }}
  {{- printf "%s: %s" $key (tpl $value $ | quote) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "keycloak.selectorLabels" $ | nindent 6 }}
      app.kubernetes.io/component: http
  endpoints:
    - port: {{ .port }}
      path: {{ .path }}
      interval: {{ .interval }}
      scrapeTimeout: {{ .scrapeTimeout }}
{{- end }}
{{- end -}}
