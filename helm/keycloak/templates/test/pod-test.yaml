{{- if .Values.test.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ include "keycloak.fullname" . }}-test
  labels:
    {{- include "keycloak.labels" . | nindent 4 }}
    app.kubernetes.io/component: test
  annotations:
    helm.sh/hook: test
spec:
  securityContext:
    {{- toYaml .Values.test.podSecurityContext | nindent 4 }}
  containers:
    - name: keycloak-test
      image: "{{ .Values.test.image.repository }}:{{ .Values.test.image.tag }}"
      imagePullPolicy: {{ .Values.test.image.pullPolicy }}
      securityContext:
        {{- toYaml .Values.test.securityContext | nindent 8 }}
      command:
        - python3
      args:
        - /tests/test.py
      env:
        - name: KEYCLOAK_USER
          valueFrom:
            secretKeyRef:
              name: {{ include "keycloak.fullname" . }}-admin-creds
              key: user
        - name: KEY<PERSON>OAK_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ include "keycloak.fullname" . }}-admin-creds
              key: password
      volumeMounts:
        - name: tests
          mountPath: /tests
  volumes:
    - name: tests
      configMap:
        name: {{ include "keycloak.fullname" . }}-test
  restartPolicy: Never
{{- end }}
