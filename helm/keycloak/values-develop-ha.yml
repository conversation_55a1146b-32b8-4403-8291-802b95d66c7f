image:
  repository: 400319975147.dkr.ecr.eu-west-2.amazonaws.com/develop-keycloak-ha
  tag: 13.0.0
postgresql:
  enabled: false
replicas: 2

keycloak:
  hostsname: develop-auth.questmarine.app

nodeSelector:
  on-demand: "true"

extraEnv: |
  - name: DB_VENDOR
    value: mysql
  - name: DB_ADDR
    value: questmarine-develop-rds.c3jfjjzaly5m.eu-west-2.rds.amazonaws.com
  - name: DB_PORT
    value: "3306"
  - name: DB_DATABASE
    value: keycloak_ha
  - name: KEYCLOAK_USER
    value: admin
  - name: KEYCLOAK_PASSWORD
    value: Y>0fUNX5)L2)d!
  - name: DB_USER
    value: root
  - name: DB_PASSWORD
    value: A!%^*GVDYEVU#!#^!
  - name: JDBC_PARAMS
    value: useSSL=false
  - name: JGROUPS_DISCOVERY_PROTOCOL
    value: dns.DNS_PING
  - name: JGROUPS_DISCOVERY_PROPERTIES
    value: 'dns_query={{ include "keycloak.serviceDnsName" . }}'
  - name: CACHE_OWNERS_COUNT
    value: "2"
  - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
    value: "2"
  - name: PROXY_ADDRESS_FORWARDING
    value: "true"
  - name: KEYCLOAK_IMPORT
    value: /tmp/realm-export.json
  - name: KEYCLOAK_FRONTEND_URL	
    value: https://develop-auth.questmarine.app/auth
