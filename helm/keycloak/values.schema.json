{"$schema": "http://json-schema.org/schema#", "type": "object", "required": ["image"], "definitions": {"image": {"type": "object", "required": ["repository", "tag"], "properties": {"pullPolicy": {"type": "string", "pattern": "^(Always|Never|IfNotPresent)$"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "imagePullSecrets": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}}}}}, "properties": {"affinity": {"type": "string"}, "args": {"type": "array"}, "clusterDomain": {"type": "string"}, "command": {"type": "array"}, "enableServiceLinks": {"type": "boolean"}, "extraContainers": {"type": "string"}, "extraEnv": {"type": "string"}, "extraEnvFrom": {"type": "string"}, "extraInitContainers": {"type": "string"}, "extraPorts": {"type": "array"}, "extraVolumeMounts": {"type": "string"}, "extraVolumes": {"type": "string"}, "fullnameOverride": {"type": "string"}, "hostAliases": {"type": "array"}, "image": {"$ref": "#/definitions/image"}, "imagePullSecrets": {"$ref": "#/definitions/imagePullSecrets"}, "ingress": {"type": "object", "properties": {"annotations": {"type": "object"}, "enabled": {"type": "boolean"}, "labels": {"type": "object"}, "rules": {"type": "array", "items": {"type": "object", "properties": {"host": {"type": "string"}, "paths": {"type": "array", "items": {"type": "string"}}}}}, "servicePort": {"anyOf": [{"type": "integer"}, {"type": "string"}]}, "tls": {"type": "array", "items": {"type": "object", "properties": {"hosts": {"type": "array", "items": {"items": {"type": "string"}}, "secretName": {"type": "string"}}}}}}, "lifecycleHooks": {"type": "string"}, "livenessProbe": {"type": "string"}, "nameOverride": {"type": "string"}, "nodeSelector": {"type": "object"}, "pgchecker": {"type": "object", "properties": {"image": {"$ref": "#/definitions/image"}, "resources": {"type": "object", "properties": {"limits": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}, "requests": {"type": "object", "properties": {"cpu": {"type": "string"}, "memory": {"type": "string"}}}}}, "securityContext": {"type": "object"}}}, "podAnnotations": {"type": "object"}, "podDisruptionBudget": {"type": "object"}, "podLabels": {"type": "object"}, "podManagementPolicy": {"type": "string"}, "podSecurityContext": {"type": "object"}, "postgresql": {"type": "object"}, "priorityClassName": {"type": "string"}, "prometheusRule": {"type": "object"}, "serviceMonitor": {"type": "object"}, "readinessProbe": {"type": "string"}, "replicas": {"type": "integer"}, "resources": {"type": "object"}, "restartPolicy": {"type": "string"}, "route": {"type": "object", "properties": {"annotations": {"type": "object"}, "enabled": {"type": "boolean"}, "host": {"type": "string"}, "labels": {"type": "object"}, "path": {"type": "string"}, "tls": {"type": "object"}}}, "secrets": {"type": "object"}, "securityContext": {"type": "object"}, "service": {"type": "object", "properties": {"annotations": {"type": "object"}, "extraPorts": {"type": "array"}, "httpNodePort": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "httpPort": {"type": "integer"}, "httpsNodePort": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "httpsPort": {"type": "integer"}, "httpManagementNodePort": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "httpManagementPort": {"type": "integer"}, "labels": {"type": "object"}, "nodePort": {"anyOf": [{"type": "integer"}, {"type": "null"}]}, "type": {"type": "string"}, "loadBalancerIP": {"type": "string"}}}, "serviceAccount": {"type": "object", "properties": {"annotations": {"type": "object"}, "create": {"type": "boolean"}, "imagePullSecrets": {"$ref": "#/definitions/imagePullSecrets"}, "labels": {"type": "object"}, "name": {"type": "string"}}}, "rbac": {"type": "object", "properties": {"create": {"type": "boolean"}, "rules": {"type": "array"}}}, "startupScripts": {"type": "object"}, "statefulsetAnnotations": {"type": "object"}, "statefulsetLabels": {"type": "object"}, "terminationGracePeriodSeconds": {"type": "integer"}, "test": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "image": {"$ref": "#/definitions/image"}, "podSecurityContext": {"type": "object"}, "securityContext": {"type": "object"}}}, "tolerations": {"type": "array"}}}}