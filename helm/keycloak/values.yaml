# Optionally override the fully qualified name
fullnameOverride: ""

# Optionally override the name
nameOverride: ""

# The number of replicas to create
replicas: 1

image:
  # The Keycloak image repository
  repository: docker.io/jboss/keycloak
  # Overrides the Keycloak image tag whose default is the chart version
  tag: ""
  # The Keycloak image pull policy
  pullPolicy: IfNotPresent

# Image pull secrets for the Pod
imagePullSecrets:
  - name: myRegistrKeySecretName

# Mapping between IPs and hostnames that will be injected as entries in the Pod's hosts files
hostAliases: []
# - ip: "*******"
#   hostnames:
#     - "my.host.com"

# Indicates whether information about services should be injected into Pod's environment variables, matching the syntax of Docker links
enableServiceLinks: true

# Pod management policy. One of `Parallel` or `OrderedReady`
podManagementPolicy: Parallel

# Pod restart policy. One of `Always`, `OnFailure`, or `Never`
restartPolicy: Always

serviceAccount:
  # Specifies whether a ServiceAccount should be created
  create: true
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
  # Additional annotations for the ServiceAccount
  annotations: {}
  # Additional labels for the ServiceAccount
  labels: {}
  # Image pull secrets that are attached to the ServiceAccount
  imagePullSecrets: []

rbac:
  create: false
  rules: []
  # RBAC rules for KUBE_PING
  #  - apiGroups:
  #      - ""
  #    resources:
  #      - pods
  #    verbs:
  #      - get
  #      - list

# SecurityContext for the entire Pod. Every container running in the Pod will inherit this SecurityContext. This might be relevant when other components of the environment inject additional containers into running Pods (service meshes are the most prominent example for this)
podSecurityContext:
  fsGroup: 1000

# SecurityContext for the Keycloak container
securityContext:
  runAsUser: 1000
  runAsNonRoot: true

# Additional init containers, e. g. for providing custom themes
extraInitContainers: ""

# Additional sidecar containers, e. g. for a database proxy, such as Google's cloudsql-proxy
extraContainers: ""

# Lifecycle hooks for the Keycloak container
lifecycleHooks: |
#  postStart:
#    exec:
#      command:
#        - /bin/sh
#        - -c
#        - ls

# Termination grace period in seconds for Keycloak shutdown. Clusters with a large cache might need to extend this to give Infinispan more time to rebalance
terminationGracePeriodSeconds: 60

# The internal Kubernetes cluster domain
clusterDomain: cluster.local

## Overrides the default entrypoint of the Keycloak container
command: []

## Overrides the default args for the Keycloak container
args: []

# Additional environment variables for Keycloak
extraEnv: ""
  # - name: KEYCLOAK_LOGLEVEL
  #   value: DEBUG
  # - name: WILDFLY_LOGLEVEL
  #   value: DEBUG
  # - name: CACHE_OWNERS_COUNT
  #   value: "2"
  # - name: CACHE_OWNERS_AUTH_SESSIONS_COUNT
  #   value: "2"

# Additional environment variables for Keycloak mapped from Secret or ConfigMap
extraEnvFrom: ""

#  Pod priority class name
priorityClassName: ""

# Pod affinity
affinity: |
  podAntiAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchLabels:
            {{- include "keycloak.selectorLabels" . | nindent 10 }}
          matchExpressions:
            - key: app.kubernetes.io/component
              operator: NotIn
              values:
                - test
        topologyKey: kubernetes.io/hostname
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              {{- include "keycloak.selectorLabels" . | nindent 12 }}
            matchExpressions:
              - key: app.kubernetes.io/component
                operator: NotIn
                values:
                  - test
          topologyKey: failure-domain.beta.kubernetes.io/zone
# Node labels for Pod assignment
nodeSelector: {}

# Node taints to tolerate
tolerations: []

# Additional Pod labels
podLabels: {}

# Additional Pod annotations
podAnnotations: {}

# Liveness probe configuration
livenessProbe: |
  httpGet:
    path: /auth/
    port: http
  initialDelaySeconds: 300
  timeoutSeconds: 5
# Readiness probe configuration
readinessProbe: |
  httpGet:
    path: /auth/realms/master
    port: http
  initialDelaySeconds: 30
  timeoutSeconds: 1
# Pod resource requests and limits
resources: {}
  # requests:
  #   cpu: "500m"
  #   memory: "1024Mi"
  # limits:
  #   cpu: "500m"
  #   memory: "1024Mi"

# Startup scripts to run before Keycloak starts up
startupScripts:
  # WildFly CLI script for configuring the node-identifier
  keycloak.cli: |
    {{- .Files.Get "scripts/keycloak.cli" }}
  # mystartup.sh: |
  #   #!/bin/sh
  #
  # echo 'Hello from my custom startup script!'

# Add additional volumes, e. g. for custom themes
extraVolumes: ""

# Add additional volumes mounts, e. g. for custom themes
extraVolumeMounts: ""

# Add additional ports, e. g. for admin console or exposing JGroups ports
extraPorts: []

# Pod disruption budget
podDisruptionBudget: {}
#  maxUnavailable: 1
#  minAvailable: 1

# Annotations for the StatefulSet
statefulsetAnnotations: {}

# Additional labels for the StatefulSet
statefulsetLabels: {}

# Configuration for secrets that should be created
secrets: {}
  # mysecret:
  #   annotations: {}
  #   labels: {}
  #   stringData: {}
  #   data: {}

service:
  # Annotations for headless and HTTP Services
  annotations: {}
  # Additional labels for headless and HTTP Services
  labels: {}
  # key: value
  # The Service type
  type: ClusterIP
  # Optional IP for the load balancer. Used for services of type LoadBalancer only
  loadBalancerIP: ""
  # The http Service port
  httpPort: 80
  # The HTTP Service node port if type is NodePort
  httpNodePort: null
  # The HTTPS Service port
  httpsPort: 8443
  # The HTTPS Service node port if type is NodePort
  httpsNodePort: null
  # The WildFly management Service port
  httpManagementPort: 9990
  # The WildFly management Service node port if type is NodePort
  httpManagementNodePort: null
  # Additional Service ports, e. g. for custom admin console
  extraPorts: []

ingress:
  # If `true`, an Ingress is created
  enabled: false
  # The Service port targeted by the Ingress
  servicePort: http
  # Ingress annotations
  annotations: {}
  # Additional Ingress labels
  labels: {}
   # List of rules for the Ingress
  rules:
    -
      # Ingress host
      host: '{{ .Release.Name }}.keycloak.example.com'
      # Paths for the host
      paths:
        - /
  # TLS configuration
  tls:
    - hosts:
        - keycloak.example.com
      secretName: keycloak-tls

route:
  # If `true`, an OpenShift Route is created
  enabled: false
  # Path for the Route
  path: /
  # Route annotations
  annotations: {}
  # Additional Route labels
  labels: {}
  # Host name for the Route
  host: ""
  # TLS configuration
  tls:
    # If `true`, TLS is enabled for the Route
    enabled: true
    # Insecure edge termination policy of the Route. Can be `None`, `Redirect`, or `Allow`
    insecureEdgeTerminationPolicy: Redirect
    # TLS termination of the route. Can be `edge`, `passthrough`, or `reencrypt`
    termination: edge

pgchecker:
  image:
    # Docker image used to check Postgresql readiness at startup
    repository: docker.io/busybox
    # Image tag for the pgchecker image
    tag: 1.32
    # Image pull policy for the pgchecker image
    pullPolicy: IfNotPresent
  # SecurityContext for the pgchecker container
  securityContext:
    allowPrivilegeEscalation: false
    runAsUser: 1000
    runAsGroup: 1000
    runAsNonRoot: true
  # Resource requests and limits for the pgchecker container
  resources:
    requests:
      cpu: "10m"
      memory: "16Mi"
    limits:
      cpu: "10m"
      memory: "16Mi"

postgresql:
  # If `true`, the Postgresql dependency is enabled
  enabled: true
  # PostgreSQL User to create
  postgresqlUsername: keycloak
  # PostgreSQL Password for the new user
  postgresqlPassword: keycloak
  # PostgreSQL Database to create
  postgresqlDatabase: keycloak
  # Persistent Volume Storage configuration

serviceMonitor:
  # If `true`, a ServiceMonitor resource for the prometheus-operator is created
  enabled: false
  # Optionally sets a target namespace in which to deploy the ServiceMonitor resource
  namespace: ""
  # Annotations for the ServiceMonitor
  annotations: {}
  # Additional labels for the ServiceMonitor
  labels: {}
  # Interval at which Prometheus scrapes metrics
  interval: 10s
  # Timeout for scraping
  scrapeTimeout: 10s
  # The path at which metrics are served
  path: /metrics
  # The Service port at which metrics are served
  port: http-management

prometheusRule:
  # If `true`, a PrometheusRule resource for the prometheus-operator is created
  enabled: false
  # Annotations for the PrometheusRule
  annotations: {}
  # Additional labels for the PrometheusRule
  labels: {}
  # List of rules for Prometheus
  rules: []
  # - alert: keycloak-IngressHigh5xxRate
  #   annotations:
  #     message: The percentage of 5xx errors for keycloak over the last 5 minutes is over 1%.
  #   expr: |
  #     (
  #       sum(
  #         rate(
  #           nginx_ingress_controller_response_duration_seconds_count{exported_namespace="mynamespace",ingress="mynamespace-keycloak",status=~"5[0-9]{2}"}[1m]
  #         )
  #       )
  #       /
  #       sum(
  #         rate(
  #           nginx_ingress_controller_response_duration_seconds_count{exported_namespace="mynamespace",ingress="mynamespace-keycloak"}[1m]
  #         )
  #       )
  #     ) * 100 > 1
  #   for: 5m
  #   labels:
  #     severity: warning

test:
  # If `true`, test resources are created
  enabled: false
  image:
    # The image for the test Pod
    repository: docker.io/unguiculus/docker-python3-phantomjs-selenium
    # The tag for the test Pod image
    tag: v1
    # The image pull policy for the test Pod image
    pullPolicy: IfNotPresent
  # SecurityContext for the entire test Pod
  podSecurityContext:
    fsGroup: 1000
  # SecurityContext for the test container
  securityContext:
    runAsUser: 1000
    runAsNonRoot: true