<#macro registrationLayout bodyClass="" displayInfo=false displayMessage=true displayWide=false displayRequiredFields=false showAnotherWayIfPresent=true>
<#assign logoClassMap = {
    "howden": properties.qmBrokerLogoClass!,
    "default": properties.qmLogoClass!
} />
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" class="${properties.kcHtmlClass!}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="robots" content="noindex, nofollow">
    <link rel="preload" href="${url.resourcesPath}/img/new_login_bg.jpg" as="image">

    <#if properties.meta?has_content>
        <#list properties.meta?split(' ') as meta>
            <meta name="${meta?split('==')[0]}" content="${meta?split('==')[1]}"/>
        </#list>
    </#if>
    <title>${msg("loginTitle",(realm.displayName!''))}</title>
    <link rel="icon" href="${url.resourcesPath}/img/qm-favicon.svg"/>
    <link rel="icon" href="${url.resourcesPath}/img/qm-favicon.png"/>
    <#if properties.styles?has_content>
        <#list properties.styles?split(' ') as style>
            <link href="${url.resourcesPath}/${style}?v=1.0" rel="stylesheet" />
        </#list>
    </#if>
    <#if properties.scripts?has_content>
        <#list properties.scripts?split(' ') as script>
            <script src="${url.resourcesPath}/${script}" type="text/javascript"></script>
        </#list>
    </#if>
    <#if scripts??>
        <#list scripts as script>
            <script src="${script}" type="text/javascript"></script>
        </#list>
    </#if>
</head>

<body class="${properties.kcBodyClass!}">
  <div class="${properties.kcHtmlClassOverlay!}"></div>
  <div class="${properties.kcLoginClass!}">
  <div class="${properties.qmHeaderLogoClass!}">
    <p></p>
    <a href="https://www.concirrus.ai/" target="_blank"></a>
  </div>
    <div class="${properties.kcFormCardClass!} <#if displayWide>${properties.kcFormCardAccountClass!}</#if>">
      <header class="${properties.kcFormHeaderClass!}">
        <#if realm.internationalizationEnabled  && locale.supported?size gt 1>
            <div id="kc-locale">
                <div id="kc-locale-wrapper" class="${properties.kcLocaleWrapperClass!}">
                    <div class="kc-dropdown" id="kc-locale-dropdown">
                        <a href="#" id="kc-current-locale-link">${locale.current}</a>
                        <ul>
                            <#list locale.supported as l>
                                <li class="kc-dropdown-item"><a href="${l.url}">${l.label}</a></li>
                            </#list>
                        </ul>
                    </div>
                </div>
            </div>
        </#if>
       <div id="qm-logo" class="${logoClassMap[realm.name]?default(logoClassMap['default'])}">
        </div> 
        <!--<h1 id="kc-page-title"><#nested "header"></h1>-->
      </header>
      <div id="kc-content">
        <p id="sso-headerLogo" class=""></p>
        <div id="kc-content-wrapper">
          <div id="sso" class="account-link-sso hide-verification-message">
            <p class="heading">
                ${msg("verify-your-account")}
            </p>
            <p class="text">
                ${msg("account-verification-description")}
            </p>
          </div>
          <#nested "form" displayMessage message displayInfo>
        </div>
      </div>
    </div>
  <script>
  const ssoPage = document.getElementById('kc-content');
  const ssoHeaderLogo = document.getElementById('sso-headerLogo');
  const qmLogo = document.getElementById('qm-logo');
  const totp = document.getElementById('totp');
  const userLabel = document.getElementById('userLabel');
  const saveTOTPBtn = document.getElementById('saveTOTPBtn');
  const instruction = document.getElementById('instruction1');
  const instruction2 = document.getElementById('instruction2');
  totp.style.backgroundImage = "none";
  totp.placeholder = "One-time Code*"
  userLabel.style.backgroundImage = "none";
  userLabel.placeholder = "Enter device name*";
  saveTOTPBtn.disabled = true;

  const usernameInput = document.getElementById('username');
  const emailInput = document.getElementById('email');
  const firstInput = document.getElementById('firstName');
  const lastInput = document.getElementById('lastName');
  
  if(totp){
    ssoPage.classList.add("sso-page")
    ssoHeaderLogo.classList.add("sso-header-logo")
    ssoPage.classList.add("sso-page")
    qmLogo.style.backgroundImage = "none";
  }

  if(instruction && instruction.textContent === "Verification email"){
    instruction2.style.flexDirection = "column";
  }

  function validateInputs() {
        const otp = totp.value.trim();
        const deviceName = userLabel.value.trim();
        if (otp && deviceName) {
            saveTOTPBtn.disabled = false;
        } else {
            saveTOTPBtn.disabled = true;
            }
        }
        totp.addEventListener('input', validateInputs);
        userLabel.addEventListener('input', validateInputs);

  document.addEventListener("DOMContentLoaded", function() {
    // Check for link account step
    var linkAccountButton = document.querySelector("#linkAccount");
    
    if (linkAccountButton) {
      document.querySelector('.account-link-sso').classList.remove('hide-verification-message');
    }
  });
  //This code simulate the alert error showend on login pages, that are normally throwed by backend server.
  var simulateBackendError = {
    preErrorDiv : document.getElementsByClassName('login-button-spacer')[0],
    postErrorDiv : document.getElementsByClassName('alert')[0],
    showAlertDiv: function(msg, styleClass = "alert-error") {
        if (this.preErrorDiv) {
            var errorHtml = document.createElement('div');
            errorHtml.className = "alert " + styleClass;
            errorHtml.innerHTML = '<span class="kc-feedback-text">'+ msg + '</span>';
            this.preErrorDiv.parentNode.replaceChild(errorHtml,this.preErrorDiv);
        } else if (this.postErrorDiv) {
                if (!this.postErrorDiv.classList.contains(styleClass)) {
                    //change class
                    this.postErrorDiv.className = "alert " + styleClass;
                }
            this.postErrorDiv.innerHTML = '<span class="kc-feedback-text">'+ msg + '</span>';
        }
        this.checkDivAfterChange();    
    },
    hideAlertDiv: function() {
        if (this.postErrorDiv) {
            var defaulDiv = document.createElement('div');
            defaulDiv.className = "login-button-spacer";
            this.postErrorDiv.parentNode.replaceChild(defaulDiv,this.postErrorDiv);
        }
        this.checkDivAfterChange();
    },
    checkDivAfterChange: function() {
        this.preErrorDiv = document.getElementsByClassName('login-button-spacer')[0];
        this.postErrorDiv = document.getElementsByClassName('alert')[0];
    }
  }

  if(usernameInput || firstInput){
    usernameInput.placeholder= 'Username';
    firstInput.placeholder= 'First Name';
    lastInput.placeholder= 'Last Name';
    emailInput.placeholder= 'Email address';
  }
  </script>
</body>
</html>
</#macro>
