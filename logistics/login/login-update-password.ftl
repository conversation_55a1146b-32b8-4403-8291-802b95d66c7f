<#import "template.ftl" as layout>
<@layout.registrationLayout displayInfo=true; section, displayMessage, message, displayInfo>
    <#if section = "header">
        ${msg("updatePasswordTitle")}
    <#elseif section = "form">
    <div class="kc-form-reset">
        <form id="kc-passwd-update-form" class="${properties.kcFormClass!}" action="${url.loginAction}" method="post">
            <input type="text" id="username" name="username" value="${username}" autocomplete="username" readonly="readonly" style="display:none;"/>
            <input type="password" id="password" name="password" autocomplete="current-password" style="display:none;"/>

            <div id="kc-info" class="${properties.kcSignUpClass!}">
                <div id="kc-info-wrapper" class="${properties.kcInfoAreaWrapperClass!}">
                    ${msg("resetPasswordHeader")}
                </div>
            </div>

            <div class="${properties.kcFormGroupClass!} username">
                <div class="${properties.kcInputWrapperClass!} d-flex">
                    <span id="usernameIcon"></span>
                    <input type="text" id="username" name="username" value="${username}" class="${properties.kcInputClass!}" autofocus autocomplete="username" placeholder="${msg("username")}" disabled />
                </div>
            </div>

            <div class="${properties.kcFormGroupClass!} newPassword">
                <div class="${properties.kcInputWrapperClass!} d-flex">
                    <span id="passwordIcon"></span>
                    <input type="password" id="password-new" name="password-new" class="${properties.kcInputClass!}" autofocus autocomplete="new-password" placeholder="${msg("passwordNew")}" />
                </div>
            </div>

            <div class="${properties.kcFormGroupClass!} newPassword">
                <div class="${properties.kcInputWrapperClass!} d-flex">
                    <span id="passwordIcon"></span>
                    <input type="password" id="password-confirm" name="password-confirm" class="${properties.kcInputClass!}" autocomplete="new-password" placeholder="${msg("passwordConfirm")}" />
                </div>
            </div>

            <div id="passwordConditions" class="passwordConditions">
            <span><span class="checkIcon" id="checkIcon1">&#9989;</span><span class="crossIcon" id="crossIcon1">&#10060;</span>a minimum of 8 characters</span>
            <span><span class="checkIcon" id="checkIcon2">&#9989;</span><span class="crossIcon" id="crossIcon2">&#10060;</span>1 lowercase alphabetical character</span>
            <span><span class="checkIcon" id="checkIcon3">&#9989;</span><span class="crossIcon" id="crossIcon3">&#10060;</span>1 uppercase alphabetical character</span>
            <span><span class="checkIcon" id="checkIcon4">&#9989;</span><span class="crossIcon" id="crossIcon4">&#10060;</span>1 numeric character</span>
            <span><span class="checkIcon" id="checkIcon5">&#9989;</span><span class="crossIcon" id="crossIcon5">&#10060;</span>1 special character</span>
            </div>

            <#if displayMessage && message?has_content && message.type = "error">
                <div id="resetPass-alert" class="alert alert-${message.type}">
                    <span class="kc-feedback-text">${kcSanitize(message.summary)?no_esc}</span>
                </div>
            <#else>
                <div class="login-button-spacer"></div>  
            </#if>

            <div class="${properties.kcFormGroupClass!} d-flex newPassword">
                <div id="kc-form-options" class="${properties.kcFormOptionsClass!}">
                    <div class="${properties.kcFormOptionsWrapperClass!}">
                        <span></span>
                    </div>
                </div>

                <div id="kc-form-buttons" class="${properties.kcFormButtonsClass!}">
                    <input id="reset-btn" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!}" type="submit" value="${msg("doReset")}" disabled/>
                </div>
            </div>
        </form>
        </div>
        <script>
            var passwordNew = document.getElementById("password-new");
            var passwordConfirm = document.getElementById("password-confirm");
            var passwordConditions = document.getElementById("passwordConditions");
            var crossIcon = document.getElementById("crossIcon");
            var checkIcon = document.getElementById("checkIcon");
            var form = document.getElementById("kc-passwd-update-form");
            var isSubmitAllowed = true;
            const resetBtn = document.getElementById("reset-btn");
            const alert = document.getElementById('resetPass-alert');
            const alertType = alert?.innerText;

            if(alert && alertType === "Passwords do not match"){
                alert.classList.add("alert-for-invalid-pass");
                }

            var conditions = {
                'lowercase': /[a-z]/,
                'uppercase': /[A-Z]/,
                'numeric': /\d/,
                'special': /[!@#$%^&*(),.?":{}|<>]/
            };
            var isVerified = false

        function validateInputs() {
                passwordConditions.style.display = 'flex';
                checkIcon1.style.display = 'none';
                checkIcon2.style.display = 'none';
                checkIcon3.style.display = 'none';
                checkIcon4.style.display = 'none';
                checkIcon5.style.display = 'none';
                const newPass = passwordNew.value.trim();
                const confirmPass = passwordConfirm.value.trim();
                const passwordLength = passwordNew.value.length
                const lowercase = conditions.lowercase.test(passwordNew.value)
                const uppercase = conditions.uppercase.test(passwordNew.value)
                const numeric = conditions.numeric.test(passwordNew.value)
                const special = conditions.special.test(passwordNew.value)

                if(passwordLength > 8) {
                    crossIcon1.style.display = 'none';
                    checkIcon1.style.display = 'inline';
                }
                if(lowercase){
                    crossIcon2.style.display = 'none';
                    checkIcon2.style.display = 'inline';
                }
                if(uppercase){
                    crossIcon3.style.display = 'none';
                    checkIcon3.style.display = 'inline';
                }
                if(numeric){
                    crossIcon4.style.display = 'none';
                    checkIcon4.style.display = 'inline';
                }
                if(special){
                    crossIcon5.style.display = 'none';
                    checkIcon5.style.display = 'inline';
                }
                const allConditionVerified = passwordLength > 8 && lowercase && uppercase && numeric && special;
                isVerified= allConditionVerified && passwordNew.value !== passwordConfirm.value
                if(allConditionVerified){
                    passwordConditions.style.display = 'none';
                }
                if (newPass && confirmPass && allConditionVerified) {
                    resetBtn.disabled = false;
                } else {
                    resetBtn.disabled = true;
                }
            }

            passwordNew.addEventListener('input', validateInputs);
            passwordConfirm.addEventListener('input', validateInputs);
        </script>
    </#if>
</@layout.registrationLayout>
