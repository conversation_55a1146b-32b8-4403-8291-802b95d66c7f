<#import "template.ftl" as layout>
<@layout.registrationLayout displayInfo=social.displayInfo displayInfo=(realm.password && social.providers??); section, displayMessage, message>
    <#if section = "header">
        ${msg("doLogIn")}
    <#elseif section = "form">
    <div id="kc-form">
      <div id="kc-form-wrapper">
        <#if realm.password>
            <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">

                <#if !displayMessage || !message?has_content || (message.summary != msg("accountDisabledMessage") && message.summary != msg("emailSentMessage"))>

                    <div class="inputDiv">
                        <#if usernameEditDisabled??>
                            <span id="usernameIcon"></span>
                            <input tabindex="1" id="username" class="${properties.kcInputClass!}" name="username" value="${(login.username!'')}" type="text" disabled />
                        <#else>
                            <span id="usernameIcon"></span>
                            <input tabindex="1" id="username" class="${properties.kcInputClass!}" name="username" value="${(login.username!'')}"  type="text" autofocus autocomplete="off" placeholder="${msg("loginInputPlaceholder")}" />
                            </#if>
                    </div>

                    <div class="${properties.kcFormGroupClass!}">
                        <span id="passwordIcon"></span>
                        <input tabindex="2" id="password" class="${properties.kcInputClass!}" name="password" type="password" autocomplete="off" placeholder="${msg("passwordInputPlaceholder")}" />
                    </div>
                </#if>

                <#if displayMessage && message?has_content>
                    <#if message.summary = msg("accountDisabledMessage")>
                        <div id="blocked-logo" class="${properties.qmLogoBlockedClass!}">
                            <p></p>
                            <span>${msg("blockedAccountMessageHeader")}</span>
                            <span>${msg("blockedAccountMessageContent")}</span>
                        </div>
                    <#elseif message.summary = msg("emailSentMessage")>
                        <div id="blocked-logo" class="${properties.qmLogoBlockedClass!}">
                            <p></p>
                            <span class="mail-header-text">${msg("mailSentMessageHeader")}</span>
                            <span>${msg("mailSentMessageContent", login.username)}</span>
                        </div>
                        <div class="mt-3">
                        <span class="backTo"><a href="${url.loginUrl}">${kcSanitize(msg("backToLogin"))?no_esc}</a></span>
                        </div>
                    <#else>
                        <div id="alert" class="alert alert-${message.type}">
                            <span id="login-alert" class="kc-feedback-text">${kcSanitize(message.summary)?no_esc}</span>
                        </div>
                    </#if>
                </#if>

                <div class="${properties.kcFormGroupClass!} ${properties.kcFormSettingClass!}">
                    <div id="kc-form-options">
                        <#if realm.rememberMe && !usernameEditDisabled??>
                            <div class="checkbox">
                                <label>
                                    <#if login.rememberMe??>
                                        <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox" checked> ${msg("rememberMe")}
                                    <#else>
                                        <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox"> ${msg("rememberMe")}
                                    </#if>
                                </label>
                            </div>
                        </#if>
                        </div>

                  </div>

                  <#--  <#if !displayMessage || !message?has_content>
                      <div class="login-button-spacer">
                      </div>
                  </#if>  -->

                  <#if !displayMessage || !message?has_content || (message.summary != msg("accountDisabledMessage") && message.summary != msg("emailSentMessage"))>

                      <div id="kc-form-buttons" class="${properties.kcFormGroupClass!}">
                        <input tabindex="4" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!}" name="login" id="kc-login" type="submit" value="${msg("doLogIn")}" disabled/>
                      </div>

                      <div id="forgot-password-wrapper" class="${properties.kcFormOptionsWrapperClass!}">
                          <#if realm.resetPasswordAllowed>
                              <span><a tabindex="5" href="${url.loginResetCredentialsUrl}">${msg("doForgotPassword")}</a></span>
                          </#if>
                      </div>

                  </#if>

            </form>
        </#if>
        </div>
        <#if realm.password && social.providers??>
            <div id="kc-social-providers" class="${properties.kcFormSocialAccountContentClass!} ${properties.kcFormSocialAccountClass!}">
                <ul class="${properties.kcFormSocialAccountListClass!} <#if social.providers?size gt 4>${properties.kcFormSocialAccountDoubleListClass!}</#if>">
                    <#list social.providers as p>
                        <li class="${properties.kcFormSocialAccountListLinkClass!}"><a href="${p.loginUrl}" id="zocial-${p.alias}" class="zocial ${p.providerId}"> <span>${p.displayName}</span></a></li>
                    </#list>
                </ul>
            </div>
        </#if>
      </div>
    <#elseif section = "info" >
        <#if realm.password && realm.registrationAllowed && !usernameEditDisabled??>
            <div id="kc-registration">
                <span>${msg("noAccount")} <a tabindex="6" href="${url.registrationUrl}">${msg("doRegister")}</a></span>
            </div>
        </#if>
    </#if>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginButton = document.getElementById('kc-login');
            const qmLogo = document.getElementById('qm-logo');
            const blockedLogo = document.getElementById('blocked-logo');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const alert = document.getElementById('alert');
            const alertType = alert?.innerText;

            function validateInputs() {
                const username = usernameInput.value.trim();
                const password = passwordInput.value.trim();
                if (username && password) {
                    loginButton.disabled = false;
                } else {
                    loginButton.disabled = true;
                }
            }
            
            function fetchAlert() {
                const alertMessage = 'Invalid email or password. After 5 failed attempts your account will be locked.'
                if(alert && alertType === alertMessage)
                alert.classList.add("alert-for-invalid");
            }
            fetchAlert();

            if(blockedLogo){
            qmLogo.style.display = "none";}

            usernameInput.addEventListener('input', validateInputs);
            passwordInput.addEventListener('input', validateInputs);
        });
    </script>

</@layout.registrationLayout>
