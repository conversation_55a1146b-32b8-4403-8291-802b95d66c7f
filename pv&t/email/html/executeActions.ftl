<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
  <title></title>
  <!--[if !mso]><!-- -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <!--<![endif]-->
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <style type="text/css">
    #outlook a {
      padding: 0;
    }

    .ReadMsgBody {
      width: 100%;
    }

    .ExternalClass {
      width: 100%;
    }

    .ExternalClass * {
      line-height: 100%;
    }

    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    table,
    td {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    img {
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
    }

    p {
      display: block;
      margin: 13px 0;
    }

    .status-badge {
      font-weight: normal;
      padding: 5px 12px;
      border-radius: 5px;
      display: inline-block;
      text-align: center;
      vertical-align: middle;
      font-size: 12px;
    }

    .severe {
      background-color: #f86464;
      color: white;
    }

    .warning {
      background-color: #ff9f00;
      color: white;
    }

    .ok {
      background-color: #59cea7;
      color: white;
    }

    .table-row {
      background-color: #f5f9fc;
    }

    .table-header {
      text-align: left;
      padding: 15px;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      color: #00263a;
    }

    .table-cell {
      padding: 9px;
      font-size: 14px;
    }
  </style>
  <!--[if !mso]><!-->
  <style type="text/css">
    @media only screen and (max-width: 480px) {
      @-ms-viewport {
        width: 320px;
      }

      @viewport {
        width: 320px;
      }
    }
  </style>
  <!--<![endif]-->
  <!--[if mso]>
      <xml>
        <o:OfficeDocumentSettings>
          <o:AllowPNG />
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
    <![endif]-->
  <!--[if lte mso 11]>
      <style type="text/css">
        .outlook-group-fix {
          width: 100% !important;
        }
      </style>
    <![endif]-->
  <!--[if !mso]><!-->
  <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700" rel="stylesheet" type="text/css" />
  <style type="text/css">
    @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);
  </style>
  <!--<![endif]-->
  <style type="text/css">
    @media only screen and (min-width: 480px) {
      .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }
    }
  </style>
  <style type="text/css">
    @media only screen and (max-width: 480px) {
      table.full-width-mobile {
        width: 100% !important;
      }

      td.full-width-mobile {
        width: auto !important;
      }
    }
  </style>
</head>

<body style="background-color: #f2faff; margin: 20px 0" bgcolor="#f2faff">
  <div style="background-color: #f2faff; padding: 70px 0;" bgcolor="#f2faff">
    <!--[if mso | IE]>
         <table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:600px;" width="600" >
            <tr>
               <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                  <![endif]-->
    <div style="margin: 0px auto; max-width: 640px">
      <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%">
        <tbody>
          <tr>
            <td style="
                  direction: ltr;
                  font-size: 0px;
                  padding: 30px 0;
                  text-align: center;
                  vertical-align: top;
                  background-color: #4c9ed9;
                  padding: 57px 20px;
                  text-align: center;
                  border-radius: 16px 16px 0 0;
                ">
              <!--[if mso | IE]>
                                 <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                       <td class="" style="vertical-align:top;width:600px;" >
                                          <![endif]-->
              <div class="mj-column-per-100 outlook-group-fix" style="
                    font-size: 13px;
                    text-align: left;
                    direction: ltr;
                    display: inline-block;
                    vertical-align: top;
                    width: 100%;
                  ">
                <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align: top"
                  width="100%">
                  <tr>
                    <td align="center" style="
                          font-size: 0px;
                          padding: 4px 25px;
                          word-break: break-word;
                        ">
                      <table border="0" cellpadding="0" cellspacing="0" role="presentation"
                        style="border-collapse: collapse; border-spacing: 0px">
                        <tbody>
                          <tr>
                            <td>
                              <img height="39" src="${url.resourcesUrl}/img/mailer-logo.png" srcset="
                                    ${url.resourcesUrl}/img/mailer-logo-2x.png,
                                    http://${url.resourcesUrl}/img/mailer-logo-3x.png
                                  " style="
                                    border: 0;
                                    display: block;
                                    outline: none;
                                    text-decoration: none;
                                    height: 39px;
                                    width: 100%;
                                  " width="300" />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </td>
                  </tr>
                </table>
              </div>
              <!--[if mso | IE]>
                                       </td>
                                    </tr>
                                 </table>
                                 <![endif]-->
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!--[if mso | IE]>
               </td>
            </tr>
         </table>
         <table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:600px;" width="600" >
            <tr>
               <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                  <![endif]-->
    <div style="margin: 0px auto; border-radius: 8px; max-width: 640px">
      <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation"
        style="width: 100%; border-radius: 8px">
        <tbody>
          <tr>
            <td style="
                  direction: ltr;
                  font-size: 0px;
                  text-align: center;
                  vertical-align: top;
                ">
              <!--[if mso | IE]>
                                 <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                    <tr>
                                       <td class="" width="600px" >
                                          <table align="center" border="0" cellpadding="0" cellspacing="0" class="" style="width:598px;" width="598" >
                                             <tr>
                                                <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                                                   <![endif]-->
              <div style="
                    background: #ffffff;
                    background-color: #ffffff;
                    margin: 0px auto;
                    border-radius: 8px;
                    max-width: 640px;
                  ">
                <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="
                      background: #ffffff;
                      background-color: #ffffff;
                      width: 100%;
                      border-radius: 0 0 16px 16px;
                    ">
                  <tbody>
                    <tr>
                      <td style="
                            direction: ltr;
                            font-size: 0px;
                            padding: 16px;
                            text-align: center;
                            vertical-align: top;
                          ">
                        <!--[if mso | IE]>
                                                                  <table role="presentation" border="0" cellpadding="0" cellspacing="0">
                                                                     <tr>
                                                                     <![endif]-->

                        ${kcSanitize(msg("executeActionsBodyHtml",link,
                        linkExpiration, realmName, requiredActionsText,
                        linkExpirationFormatter(linkExpiration),
                        user.getFirstName()))?no_esc}

                        <!--[if mso | IE]>
                                                                     </tr>
                                                                  </table>
                                                                  <![endif]-->
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <!--[if mso | IE]>
                                                </td>
                                             </tr>
                                          </table>
                                       </td>
                                    </tr>
                                 </table>
                                 <![endif]-->
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!--[if mso | IE]>
               </td>
            </tr>
         </table>
         <![endif]-->
  </div>
</body>

</html>