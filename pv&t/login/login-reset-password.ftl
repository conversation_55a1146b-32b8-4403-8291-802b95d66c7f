<#import "template.ftl" as layout>
<@layout.registrationLayout displayInfo=true; section, displayMessage, message, displayInfo>
    <#if section = "header">
        ${msg("emailForgotTitle")}
    <#elseif section = "form">

        <#if displayInfo>
            <div id="kc-info" class="${properties.kcSignUpClass!}">
            <div id="kc-info-wrapper" class="${properties.kcInfoAreaWrapperClass!} ${properties.kcInfoAreaWrapperResetClass!}">
                ${msg("emailInstruction")}
            </div>
            </div>
        </#if>
<div class="kc-form">
    <div class="kc-form-wrapper">

        <form id="kc-reset-password-form" class="${properties.kcFormClass!}" action="${url.loginAction}" method="post">
            <div class="${properties.kcFormGroupClass!}">
                <div class="${properties.kcInputWrapperClass!} ${properties.kcInputWrapperFlexClass!}">
                    <span id="usernameIcon"></span>
                    <input type="text" id="usernameReset" name="username" class="${properties.kcInputClass!}" autofocus placeholder="${msg("loginInputPlaceholder")}" autocomplete="off"/>
                </div>
            </div>

            <#if displayMessage && message?has_content>
                <div class="alert alert-${message.type}">
                    <span class="kc-feedback-text">${kcSanitize(message.summary)?no_esc}</span>
                </div>
            <#else>
                <div class="login-button-spacer" id="errorField"></div>
            </#if>

            <div class="${properties.kcFormGroupClass!} ${properties.kcFormSettingClass!}">
                <div id="kc-form-buttons" class="${properties.kcFormButtonsClass!}">
                    <input id="resetSubmit" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonLargeClass!}" type="submit" value="${msg("senLink")}" disabled/>
                </div>

                <div id="kc-form-options" class="${properties.kcFormOptionsClass!} backToLogin">
                    <div class="${properties.kcFormOptionsWrapperClass!}">
                        <span class="backTo"><a href="${url.loginUrl}">${kcSanitize(msg("backToLogin"))?no_esc}</a></span>
                    </div>
                </div>
            </div>
        </form>
        </div>
</div>
        <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Mail Validation
            var input = document.getElementById("username");
            var form = document.getElementById("kc-reset-password-form");
            var validateEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
/*
            form.addEventListener('submit',function(event){
                if (!validateEmail.test(input.value)) {
                    //Invalid Email Address
                    event.preventDefault();
                    simulateBackendError.showAlertDiv("Invalid Email Address.");
                    
                } else simulateBackendError.hideAlertDiv();
            });
*/
        });

        const loginButton = document.getElementById('resetSubmit');
        const usernameInput = document.getElementById('usernameReset');

        function validateInputs() {
                const username = usernameInput.value.trim();
                if (username) {
                    loginButton.disabled = false;
                } else {
                    loginButton.disabled = true;
                }
            }

            usernameInput.addEventListener('input', validateInputs);
        </script>
    </#if>
</@layout.registrationLayout>
