## Email templates
Email templates are written with MJML framework and are located in insurer-front-app repository (********************************:questmotor/insurer-front-app.git) in `emailTemplates` folder.

### How to apply email theme?
To apply a html theme for the selected email you need to provide proper key-value pairs in `messages/messages_en.properties` file.

E.g. for reset password email following keys are required:
- **passwordResetSubject** - a title of the email
- **passwordResetBody** - text body (fallback)
- **passwordResetBodyHtm** - html body (should be minified to be located only in one line)
