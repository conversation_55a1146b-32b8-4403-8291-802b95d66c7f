/* transition timings */
/* breakpoints */
/* container sizes */
/* navigation */
/* animations */
.login-pf body {
  margin: 0;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  color: #fff;
  line-height: 1.5;
  font-size: 1rem;
  background-image: url(../img/new_login_bg.svg);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 100vh;
  overflow: hidden;
}

.login-pf-overlay {
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background: linear-gradient(0.25turn, #004a6a8a, #0010278f);
}

a {
  display: flex;
  justify-content: center;
  color: #fff;
  text-decoration: underline;
}
a:hover {
  color: #f2faff;
  text-decoration: none;
}

a.button,
.button {
  display: inline-block;
  height: 41px;
  min-width: 161px;
  padding: 0.5rem 1rem;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  border: 1px solid #fff;
  border-radius: 3px;
  font-size: 1rem;
  font-weight: normal;
  line-height: 1.25;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  margin: 0;
  cursor: pointer;
  touch-action: manipulation;
  text-transform: none;
  box-sizing: border-box;
  user-select: none;
  transition: padding 0.2s ease-in-out, min-width 0.2s ease-in-out,
    font-size 0.2s ease-in-out, color 0.2s ease-in-out,
    border-radius 0.2s ease-in-out;
}
a.button svg g,
.button svg g {
  transition: stroke 175ms ease-in-out;
}
a.button-light,
.button-light {
  background-color: #cbedfd;
  color: #00263a;
}
a.button-light:focus,
a.button-light:hover,
.button-light:focus,
.button-light:hover {
  color: #fff;
  background: #006489;
  border: 1px solid #006489;
  border-radius: 3px;
  -webkit-transition: background-color 0.2s ease-in-out;
  transition: background-color 0.2s ease-in-out;
}
a.button-light:focus svg g,
a.button-light:hover svg g,
.button-light:focus svg g,
.button-light:hover svg g {
  stroke: #fff;
}
a.button-dark,
.button-dark {
  background-color: #007fae;
  color: #fff;
}
a.button.auto .content,
.button.auto .content {
  padding-right: 10px;
}
a.button.error,
.button.error {
  border: solid 1px #f86464;
}

.form-control[type="text"],
.form-control[type="email"],
.form-control[type="password"] {
  display: block;
  font-size: 16px;
  width: 92%;
  margin: auto;
  background-color: #f8fcff;
  padding: 0.5rem;
  line-height: 1.25;
  border: 1px solid rgba(0, 0, 0, 0);
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
/* .form-control[type="text"]::placeholder,
  .form-control[type="password"]::placeholder {
    font-style: normal;
    color: #006489;
   } */

.credentials-error {
  color: #f86464;
}
.form-control:focus {
  color: #464a4c;
  background-color: #fff;
  border-color: #5cb3fd;
  border: 1px solid transparent;
  outline: none;
}
.form-control::-webkit-input-placeholder {
  font-style: normal;
  color: rgba(0, 100, 137, 0.5);
}

.form-control::-moz-placeholder {
  font-style: normal;
  color: rgba(0, 100, 137, 0.5);
}

.form-control:-ms-input-placeholder {
  font-style: normal;
  color: rgba(0, 100, 137, 0.5);
}

.form-control:-moz-placeholder {
  font-style: normal;
  color: rgba(0, 100, 137, 0.5);
}

.form-control:placeholder {
  font-style: normal;
  color: rgba(0, 100, 137, 0.5);
}

.login-pf-page .login-pf-header {
  margin-bottom: 4.5rem;
}

.inputDiv #usernameIcon {
  background-color: #f8fcff;
  background-image: url(../img/account.svg);
  background-repeat: no-repeat;
  background-position: center;
  padding: 0.5rem 1rem;
  margin: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  margin-right: -17px;
}

#usernameIcon {
  background-color: #f8fcff;
  background-image: url(../img/account.svg);
  background-repeat: no-repeat;
  background-position: center;
  padding: 0.5rem 1rem;
  margin: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  margin-right: -17px;
}

#usernameReset {
  width: 17.5rem;
  margin-left: 15px;
}
#resetSubmit {
  width: 20.5rem;
}

.reset-info-msg {
  max-width: 60%;
  position: relative;
  left: 20%;
}

.form-group {
  margin-bottom: 10px !important;
  text-align: center;
  display: flex;
  justify-content: center;
}
.newPassword {
  justify-content: center;
}
.newPassword #password-new,
#password-confirm {
  width: 17.5rem;
  margin-left: 15px;
}
.newPassword #reset-btn {
  width: 20.5rem;
}

.form-group label {
  text-align: start;
}

.form-group #passwordIcon {
  background-color: #f8fcff;
  background-image: url(../img/password.svg);
  background-repeat: no-repeat;
  background-position: center;
  padding: 0.5rem 1rem;
  margin: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  margin-right: -17px;
  z-index: 99;
}

#kc-content {
  margin-left: auto;
  margin-right: auto;
}
#kc-content-wrapper {
  text-align: center;
}

#forgot-password-wrapper {
  margin-top: 20px;
  text-align: center;
  font-size: 16px;
}

#forgot-password-wrapper span {
  font-size: 16px;
  color: #fff;
}

#forgot-password-wrapper a {
  font-size: 16px;
  color: #fff;
  text-decoration: none;
}

#forgot-password-wrapper span:hover {
  text-decoration: underline;
  color: #fff;
}

.alert.alert-error {
  background-color: #f86464;
  border-color: #f86464;
  color: #fff;
  font-weight: 300;
  text-align: center;
}

.alert {
  padding: 7px;
  margin-bottom: 0;
  border-radius: 0.5rem;
}
.alert-for-invalid {
  position: absolute;
  left: 25%;
  bottom: -20%;
  width: fit-content;
  font-size: 12px;
}
.alert-for-invalid-pass {
  position: absolute;
  left: 32.5%;
  bottom: -10%;
  width: fit-content;
  font-size: 12px;
}

/* .login-button-spacer {
  padding-top: 34px; } */

.custom-message-header {
  width: 406px;
  margin-top: 24px;
  margin-left: -53px;
  font-size: 22px;
  font-weight: 300;
  text-align: center;
}

.custom-message-content {
  width: 406px;
  margin-top: 25px;
  margin-left: -53px;
  font-size: 16px;
  text-align: center;
}

#kc-reset-password-form {
  width: 300px;
  margin: auto;
}

#kc-error-message,
#kc-info-message {
  margin-top: 28px;
  text-align: center;
}
#kc-error-message .instruction,
#kc-info-message .instruction {
  margin-bottom: 28px;
}

#kc-form-login .form-group:first-child {
  padding-top: 4px;
}

.main-login-form {
  margin-top: 24px;
}

.login-pf-page .login-pf-settings {
  margin-bottom: 0 !important;
}

#kc-locale ul {
  position: absolute;
  top: 20px;
  right: 0;
  display: none;
  min-width: 100px;
  padding: 2px 0;
  background-color: #fff;
  border: solid 1px #bbb;
  list-style: none;
}

#kc-locale:hover ul {
  display: block;
  margin: 0;
}

#kc-locale ul li a {
  display: block;
  padding: 5px 14px;
  color: #000 !important;
  line-height: 20px;
  text-decoration: none;
}

#kc-locale ul li a:hover {
  background-color: #d4edfa;
  color: #4d5258;
}

#kc-locale-dropdown a {
  padding: 0 15px 0 0;
  background: 0 0;
  color: #4d5258;
  font-weight: 300;
}

#kc-locale-dropdown a:hover {
  text-decoration: none;
}

a#kc-current-locale-link {
  display: block;
  padding: 0 5px;
}

/* a#kc-current-locale-link:hover {
    background-color: rgba(0,0,0,0.2);
} */
a#kc-current-locale-link::after {
  margin-left: 4px;
  content: "\2c5";
}

.login-pf .container {
  padding-top: 40px;
}

.login-pf a:hover,
a:hover {
  color: #fff;
}

#kc-logo {
  width: 100%;
}

#kc-logo-wrapper {
  height: 63px;
  width: 300px;
  margin: 62px auto 0;
}

div.kc-logo-text {
  height: 63px;
  width: 300px;
  margin: 0 auto;
}

div.kc-logo-text span {
  display: none;
}

div.qm-logo-text {
  height: 100px;
  width: 367px;
  margin: 0 auto;
  background-image: url("../img/quest_cargo.svg");
  background-repeat: no-repeat;
}

div.qm-logo-blocked {
  display: flex;
  flex-direction: column;
  text-align: center;
  justify-content: center;
  align-items: center;
  width: 100%;
  background: #8f8f8f6b;
  padding: 15px;
  border-radius: 0.5rem;
  position: relative;
  left: -5%;
}
div.qm-logo-blocked p {
  height: 37px;
  width: 199px;
  background-image: url("../img/quest_cargo-small.svg");
  background-repeat: no-repeat;
}
div.qm-logo-blocked span {
  color: #fff;
}
div.qm-logo-blocked span:first-of-type {
  font-size: 14px;
}
div.qm-logo-blocked span:nth-child(3) {
  font-size: 12px;
}
div.qm-logo-blocked .mail-header-text {
  font-size: 20px !important;
  font-weight: 500;
  margin-bottom: 15px;
}
div.qm-logo-blocked .mail-header-subText {
  font-size: 12px !important;
}

div.qm-header-logo {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}
div.qm-header-logo p {
  position: absolute;
  top: 4%;
  left: 4%;
  height: 21px;
  width: 137px;
  background-image: url("../img/concirrus_logo.svg");
  background-repeat: no-repeat;
}
div.qm-header-logo a {
  position: absolute;
  top: 4%;
  right: 4%;
  height: 30px;
  width: 30px;
  background-image: url("../img/globe.svg");
  background-repeat: no-repeat;
  cursor: pointer;
}

#kc-header {
  color: #ededed;
  overflow: visible;
  white-space: nowrap;
}

#kc-header-wrapper {
  height: 145px;
  padding: 62px 10px 20px;
  font-size: 29px;
  letter-spacing: 3px;
  line-height: 1.2em;
  text-transform: uppercase;
  white-space: normal;
}

#kc-content {
  width: 100%;
  margin-top: -50px;
  overflow-y: auto;
  max-height: 50vh;
  padding-right: 10px;
}

#instruction3 {
  display: none;
}

#kc-info {
  padding-bottom: 200px;
  margin-bottom: -200px;
}

#kc-info-wrapper {
  margin-top: 0;
  margin-bottom: 1rem;
  text-align: center !important;
  color: #fff !important;
  font-size: 15.9px;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  box-sizing: inherit;
}

#kc-form-options span {
  display: block;
  margin-top: 25px;
  text-align: center;
}

.backToLogin {
  position: absolute;
  top: 90%;
  left: 50%;
  transform: translate(-50%, 50%);
}

#kc-form-options .checkbox {
  margin-top: 0;
  color: #007fae;
}

#kc-terms-text {
  margin-bottom: 20px;
}

#kc-registration {
  margin-bottom: 15px;
}

/* TOTP */
ol#kc-totp-settings {
  padding-left: 20px;
  margin: 0;
}

ul#kc-totp-supported-apps {
  margin-bottom: 10px;
}

#kc-totp-secret-qr-code {
  max-height: 150px;
  max-width: 150px;
}

#kc-totp-secret-key {
  padding: 10px 0;
  background-color: #fff;
  color: #333;
  font-size: 16px;
}

/* OAuth */
#kc-oauth h3 {
  margin-top: 0;
}

#kc-oauth ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

#kc-oauth ul li {
  padding: 10px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px;
}

#kc-oauth ul li:first-of-type {
  border-top: 0;
}

#kc-oauth .kc-role {
  display: inline-block;
  width: 50%;
}

/* Code */
#kc-code textarea {
  height: 8em;
  width: 100%;
}

/* Social */
#kc-social-providers ul {
  padding: 0;
}

#kc-social-providers li {
  display: block;
}

#kc-social-providers li:first-of-type {
  margin-top: 0;
}

.zocial,
a.zocial {
  width: 80%;
  background: #f5f5f5;
  border: 0;
  border-radius: 0;
  color: #007fae;
  font-size: 14px;
  font-weight: normal;
  white-space: normal;
  text-shadow: none;
}

.zocial:before {
  margin-right: 0;
  border-right: 0;
}

.zocial span:before {
  padding: 7px 10px;
  font-size: 14px;
}

.zocial:hover {
  background: radial-gradient(
    ellipse 20% 200% at center,
    #5f5f5f9e 25%,
    grey 100%,
    #5f5f5f9e 50%
  ) !important;
}

.zocial.facebook,
.zocial.github,
.zocial.google,
.zocial.microsoft,
.zocial.stackoverflow,
.zocial.linkedin,
.zocial.twitter {
  background-image: none;
  border: 0;
  box-shadow: none;
  text-shadow: none;
}

/* Copy of zocial windows classes to be used for microsoft's social provider button */
.zocial.microsoft:before {
  content: "\f15d";
}

.zocial.stackoverflow:before {
  color: inherit;
}

@media (min-width: 768px) {
  #kc-container-wrapper {
    position: absolute;
    width: 100%;
  }
  .login-pf .container {
    padding-right: 80px;
  }
  #kc-locale {
    position: relative;
    z-index: 9999;
    text-align: right;
  }
}

@media (max-width: 767px) {
  #kc-header {
    padding-left: 15px;
    padding-right: 15px;
    text-align: left;
    float: none;
  }
  #kc-header-wrapper {
    height: 90px;
    padding: 20px 60px 0 0;
    color: #007fae;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0;
  }
  div.kc-logo-text {
    height: 90px;
    width: 150px;
    margin: 0;
    background-size: 100%;
  }
  #kc-form {
    float: none;
  }
  #kc-info-wrapper {
    padding-top: 15px;
    padding-left: 0;
    padding-right: 15px;
    margin-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  #kc-social-providers li {
    display: block;
    margin-right: 5px;
  }
  .login-pf .container {
    padding-top: 15px;
    padding-bottom: 15px;
  }
  #kc-locale {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 200px;
    text-align: right;
  }
  #kc-logo-wrapper {
    height: 21px;
    width: 100px;
    margin: 20px 0 0 20px;
    background-size: 100px 21px;
  }
}

@media (min-height: 646px) {
  #kc-container-wrapper {
    bottom: 12%;
  }
}

@media (max-height: 645px) {
  #kc-container-wrapper {
    top: 20%;
    padding-top: 50px;
  }
}

.card-pf form.form-actions .btn {
  margin-left: 10px;
  float: right;
}

#kc-form-buttons {
  height: 41px;
  margin-top: 30px;
  text-align: center;
}

.login-pf-page .login-pf-brand {
  width: 40%;
  max-width: 360px;
  margin-top: 20px;
}

.card-pf {
  box-sizing: content-box;
  width: 300px;
  padding: 0 20px;
  margin: 0 auto;
  background: transparent;
  border-top: 0;
  box-shadow: 0 0 0;
}

/*tablet*/
@media (max-width: 840px) {
  .login-pf-page .card-pf {
    padding: 20px 20px 30px 20px;
  }
}

@media (max-width: 767px) {
  #kc-info-wrapper {
    width: 300px;
    margin-left: auto;
  }
  .custom-message-header {
    width: 300px;
    margin-left: auto;
  }
  .custom-message-content {
    width: 300px;
    margin-left: auto;
  }
  .login-pf-page .card-pf {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }
}

.login-pf-page .login-pf-signup {
  margin-top: 14px;
}

#kc-content-wrapper .row {
  margin-left: 0;
  margin-right: 0;
}

/* @media (min-width: 768px) {
  .login-pf-page .login-pf-social-section:first-of-type {
    padding-right: 39px;
    margin-right: -1px;
    border-right: 1px solid #d1d1d1; }
  .login-pf-page .login-pf-social-section:last-of-type {
    padding-left: 40px; }
  .login-pf-page .login-pf-social-section .login-pf-social-link:last-of-type {
    margin-bottom: 0; }
  .login-pf body {
    background-image: url("../img/stone-bottom.svg"), url("../img/cloud-1.svg"), url("../img/cloud-2.svg"), url("../img/cloud-3.svg");
    background-position: right bottom, 140px 151px, calc(100% - 158.2px) 119px, calc(100% - 94.4px) 106px;
    background-size: auto, 186.8px 95.8px, 280.8px 134.7px, 136.6px 89px; } } */

.login-pf-page .login-pf-social-link {
  margin-bottom: 25px;
}

.login-pf-page .login-pf-social-link a {
  padding: 5px;
  background: #5f5f5f9e;
  color: #fff;
  border-radius: 0.5rem;
  position: absolute;
  bottom: -35%;
  left: 9%;
}

.login-pf-page.login-pf-page-accounts {
  margin-left: auto;
  margin-right: auto;
}

.login-pf-page .btn-primary {
  margin-top: 0;
  border: none;
  border-radius: 0.5rem;
  background: #009ade;
  color: #fff;
  width: 20.5rem;
}
.login-pf-page .btn-primary:hover {
  background: radial-gradient(
    ellipse 28% 200% at center,
    #009ade 25%,
    #3abbf2 100%,
    #009ade 50%
  );
}
.login-pf-page .btn-primary:disabled {
  color: #ffffff42;
}

.login-pf-page {
  align-items: center;
  max-height: 100vh;
}
#kc-header {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  margin-bottom: 0 !important;
}
/* @media (max-width: 767px){
  .login-pf-page {
    align-items: flex-start;
  }
} */
@media (max-height: 640px) {
  .login-pf-page {
    top: -6rem;
  }
}
@media (max-height: 640px) {
  .login-pf-page {
    align-items: flex-start;
    top: 0;
  }
}

.login-pf-page .card-pf {
  padding: 0 !important;
}

#kc-form-wrapper {
  width: 300px;
  margin: auto;
  position: relative;
  z-index: 99;
}

.inputDiv {
  margin-top: 5px;
  margin-bottom: 30px;
  display: flex;
}

.form-control::-webkit-input-placeholder {
  color: #006489;
}

.form-control::-moz-placeholder {
  color: #006489;
}

.form-control:-ms-input-placeholder {
  color: #006489;
}

.form-control:-moz-placeholder {
  color: #006489;
}

.form-control:placeholder {
  color: #006489;
}
button,
input,
optgroup,
select,
textarea {
  font-family: "Roboto", sans-serif;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: inset 0 0 0px 9999px #f8fcff;
  -moz-box-shadow: inset 0 0 0px 9999px #f8fcff;
  -ms-box-shadow: inset 0 0 0px 9999px #f8fcff;
  box-shadow: inset 0 0 0px 9999px #f8fcff;
  border-color: #f8fcff !important;
  background-color: none;
}

input:-webkit-autofill:focus,
textarea:-webkit-autofill:focus,
select:-webkit-autofill:focus {
  border-color: #f8fcff !important;
}
.wave-top {
  background: url("../img/wave-top.svg") repeat-x 0 0;
  height: 181px;
  -webkit-animation: move-left 2s linear infinite;
  animation: move-left 2s linear infinite;
}

.wave-mid {
  background: url("../img/wave-mid.svg") repeat-x 0 0;
  height: 133px;
  -webkit-animation: move-right 2s linear infinite;
  animation: move-right 2s linear infinite;
}

.wave-bottom {
  background: url("../img/wave-bottom.svg") repeat-x 0 0;
  height: 72px;
}

.stone {
  position: absolute;
  bottom: 0;
  right: 0;
}

.stone-bottom {
  background: url("../img/stone-bottom.svg") no-repeat right bottom;
  height: 177px;
  width: 60%;
}

.stone-top {
  background: url("../img/tower-stone.svg") no-repeat right bottom;
  height: 456px;
  width: 40%;
  bottom: 100px;
}

@-webkit-keyframes move-left {
  from {
    background-position: -38px 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes move-left {
  from {
    background-position: -38px 0;
  }
  to {
    background-position: 0 0;
  }
}

@-webkit-keyframes move-right {
  from {
    background-position: 38px 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes move-right {
  from {
    background-position: 38px 0;
  }
  to {
    background-position: 0 0;
  }
}

@-webkit-keyframes flip {
  0% {
  }
  100% {
    -webkit-transform: rotateX(0deg);
    opacity: 1;
  }
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* Animation Css End */

[class^="qs-icon-"],
[class*=" qs-icon-"] {
  background-image: url("../images/sprite-small-icon.svg");
  background-repeat: no-repeat;
  background-position: 0 0;
  display: inline-block;
  width: 20px;
  height: 20px;
}

.backTo {
  font-size: 16px;
}
.backTo a {
  color: #fff;
  text-decoration: none;
}
.backTo a:hover {
  color: #fff;
  text-decoration: underline;
}
.kc-form-wrapper {
  width: 300px;
  margin: 0 auto;
}

#kc-content {
  overflow-y: visible;
  max-height: none;
}

.card-pf {
  z-index: 999;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;
}

.account-link-sso {
  max-width: 480px;
  margin: 0 auto;
  text-align: center;
}

#instruction {
  flex-direction: column;
}

.heading,
#instruction1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px;
  flex-direction: column;
}

#instruction2 > a {
  text-transform: lowercase;
}

.text {
  margin: 0;
}

#updateProfile,
.hide-verification-message {
  display: none;
}

#linkAccount {
  margin-top: 0;
  border: none;
  border-radius: 0.5rem;
  background: #009ade;
  color: #fff;
}

#linkAccount:hover {
  background: radial-gradient(
    ellipse 28% 200% at center,
    #009ade 25%,
    #3abbf2 100%,
    #009ade 50%
  );
}

.instruction {
  text-align: center;
  color: #fff;
  display: flex;
  justify-content: center;
}

#instruction3 {
  display: none;
}

.login-pf-social-link > .zocial {
  display: block;
  text-align: center;
  text-decoration: none;
  font: bold 100%/2.1 "Lucida Grande", Tahoma, sans-serif;
  font-size: 14px;
  font-weight: normal;
  cursor: pointer;
  color: #007fae;
  white-space: normal;
}

#loginRestartLink {
  width: 100%;
}

#loginContinueLink {
  width: 100%;
}

.sso-page {
  background: #8f8f8f6b;
  width: 35vw !important;
  /* background-image: url(../img/quest_marine.svg); */
  background-repeat: no-repeat;
  background-size: 200px 65px;
  padding: 3.5rem !important;
  padding-top: 0.5rem !important;
  padding-bottom: 2.5rem !important;
  background-position: center top;
  border-radius: 20px;
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate(-50%, 0%);
  overflow: scroll;
  height: 73.5vh;
}

.sso-header-logo {
  background-image: url(../img/quest_cargo.svg);
  background-repeat: no-repeat;
  background-size: 200px 65px;
  height: 65px;
  background-position: center;
  margin: 0px;
}

ol#kc-totp-settings {
  width: 35vw;
  text-align: start;
}
#kc-totp-settings a {
  display: flex;
  justify-content: start;
  color: #3cd2fa;
  text-decoration: underline;
}
#kc-totp-settings a:hover {
  color: #3cd2fa;
  text-decoration: none;
}
#kc-totp-settings-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
#kc-totp-settings-form .control-label,
.required {
  display: none;
}
#saveTOTPBtn {
  width: 75%;
  margin-top: 30px;
}
#totp,
#userLabel {
  border-radius: 0.5rem;
  width: 25vw;
}
#totp::placeholder,
#userLabel::placeholder {
  color: #c7d0da;
  font-size: 14px;
}

.d-flex {
  display: flex;
}

#username,
#password,
#firstName,
#lastName {
  border-radius: 0.5rem;
  width: 17.5rem;
  margin-left: 10px;
}

#email {
  border-radius: 0.5rem;
  width: 17.5rem;
  margin-left: 45px;
}

#sso p {
  text-align: center;
}

input:focus {
  outline: none; /* Remove the focus outline */
}

.mt-3 {
  margin-top: 1.5rem !important;
}

.passwordConditions {
  display: none;
  justify-content: center;
  flex-direction: column;
  text-align: left;
  background: #8f8f8f6b;
  border-radius: 10px;
  padding: 10px;
  width: 66%;
  position: relative;
  left: 15%;
}

.crossIcon,
.checkIcon {
  margin-right: 5px;
}
