parent=base
styles=css/login.css 
import=common/keycloak

styles=lib/zocial/zocial.css css/login.css 
meta=viewport==width=device-width,initial-scale=1

kcHtmlClass=login-pf
kcHtmlClassOverlay=login-pf-overlay
kcLoginClass=login-pf-page

kcLogoLink=http://www.keycloak.org

kcLogoClass=qm-logo-text
qmLogoClass=qm-logo-text
qmBrokerLogoClass=qm-broker-logo-text
qmHeaderLogoClass=qm-header-logo
qmLogoBlockedClass=qm-logo-blocked
qmSSOClass=qm-sso

kcContainerClass=container-fluid
kcContentClass=col-sm-8 col-sm-offset-2 col-md-6 col-md-offset-3 col-lg-6 col-lg-offset-3
kcContentWrapperClass=row

kcHeaderClass=login-pf-page-header
kcFeedbackAreaClass=col-md-12
kcLocaleClass=col-xs-12 col-sm-1
kcAlertIconClasserror=pficon pficon-error-circle-o

kcFormAreaClass=col-sm-10 col-sm-offset-1 col-md-8 col-md-offset-2 col-lg-8 col-lg-offset-2
kcFormCardClass=card-pf
kcFormCardAccountClass=login-pf-accounts
kcFormSocialAccountClass=login-pf-social-section
kcFormSocialAccountContentClass=col-xs-12 col-sm-6
kcFormSocialAccountListClass=login-pf-social list-unstyled login-pf-social-all
kcFormSocialAccountDoubleListClass=login-pf-social-double-col
kcFormSocialAccountListLinkClass=login-pf-social-link
kcFormHeaderClass=login-pf-header
kcCustomMessageHeaderClass=custom-message-header
kcCustomMessageContentClass=custom-message-content

kcFeedbackErrorIcon=pficon pficon-error-circle-o
kcFeedbackWarningIcon=pficon pficon-warning-triangle-o
kcFeedbackSuccessIcon=pficon pficon-ok
kcFeedbackInfoIcon=pficon pficon-info


kcFormClass=form-horizontal
kcFormGroupClass=form-group
loginFormGroupClass=main-login-form
kcFormGroupErrorClass=has-error
kcLabelClass=control-label
kcLabelWrapperClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcInputClass=form-control
kcInputWrapperClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcInputWrapperFlexClass=d-flex
kcFormOptionsClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcFormButtonsClass=col-xs-12 col-sm-12 col-md-12 col-lg-12
kcFormSettingClass=login-pf-settings
kcTextareaClass=form-control
kcSignUpClass=login-pf-signup
kcInfoAreaWrapperResetClass= reset-info-msg 


kcInfoAreaClass=col-xs-12 col-sm-4 col-md-4 col-lg-5 details

##### css classes for form buttons
# main class used for all buttons
kcButtonClass=button
# classes defining priority of the button - primary or default (there is typically only one priority button for the form)
kcButtonPrimaryClass=btn-primary
kcButtonDefaultClass=btn-default
# classes defining size of the button
kcButtonLargeClass=btn-lg
kcButtonBlockClass=btn-block

##### css classes for input
kcInputLargeClass=input-lg

##### css classes for form accessability
kcSrOnlyClass=sr-only
