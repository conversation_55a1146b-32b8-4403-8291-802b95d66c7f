/* transition timings */
/* breakpoints */
/* container sizes */
/* navigation */
/* animations */
.login-pf body {
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  color: #00263A;
  line-height: 1.5;
  font-size: 1rem;
  background: url(../img/new_login_bg.jpg);
}

a {
  color: #007fae; }
  a:hover {
    color: #f2faff;
    text-decoration: none; }

a.button, .button {
  display: inline-block;
    height: 41px;
    min-width: 161px;
    padding: 0.5rem 1rem;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    transform: translate3d(0,0,0);
    border: 1px solid #006489;
    border-radius: 3px;
    font-size: 1rem;
    font-weight: normal;
    line-height: 1.25;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    margin: 0;
    cursor: pointer;
    touch-action: manipulation;
    text-transform: none;
    box-sizing: border-box;
    user-select: none;
    transition: padding 0.2s ease-in-out, min-width 0.2s ease-in-out, font-size 0.2s ease-in-out, color 0.2s ease-in-out, border-radius 0.2s ease-in-out; }
  a.button svg g, .button svg g {
    transition: stroke 175ms ease-in-out; }
  a.button-light, .button-light {
    background-color: #CBEDFD;
    color: #00263A; }
    a.button-light:focus, a.button-light:hover, .button-light:focus, .button-light:hover {
      color: #fff;
    background: #006489;
    border: 1px solid #006489;
    border-radius: 3px;
    -webkit-transition: background-color 0.2s ease-in-out;
    transition: background-color 0.2s ease-in-out; }
      a.button-light:focus svg g, a.button-light:hover svg g, .button-light:focus svg g, .button-light:hover svg g {
        stroke: #fff; }
  a.button-dark, .button-dark {
    background-color: #007fae;
    color: #fff; }
  a.button.auto .content, .button.auto .content {
    padding-right: 10px; }
  a.button.error, .button.error {
    border: solid 1px #f86464; }

.form-control[type="text"],
.form-control[type="email"],
.form-control[type="password"] {
    display: block;
    font-size: 16px;
    width: 92%;
    margin: auto;
    background: #F8FCFF;
    padding: 0.5rem 0.75rem;
    line-height: 1.25;
    border: 1px solid rgba(0, 0, 0, 0);
    border-radius: 0.25rem;
  }
  /* .form-control[type="text"]::placeholder,
  .form-control[type="password"]::placeholder {
    font-style: normal;
    color: #006489;
   } */
.form-control:focus {
    color: #464a4c;
    background-color: #fff;
    border-color: #5cb3fd;
    border: 1px solid rgba(92, 179, 253, 1);
    outline: none;
}
.form-control::-webkit-input-placeholder {
  font-style: normal;
  color: rgba(0,100,137, 0.5);
}

.form-control::-moz-placeholder {
  font-style: normal;
  color: rgba(0,100,137, 0.5);
}

.form-control:-ms-input-placeholder {
  font-style: normal;
  color: rgba(0,100,137, 0.5);
}

.form-control:-moz-placeholder {
  font-style: normal;
  color: rgba(0,100,137, 0.5);
}

.form-control:placeholder {
  font-style: normal;
  color: rgba(0,100,137, 0.5);
}
.login-pf-page .login-pf-header {
  margin: 40px;
}

.form-group {
  margin-bottom: 10px !important;
  text-align: center;
}

#kc-content {
  margin-left: auto;
  margin-right: auto; }

#forgot-password-wrapper {
  margin-top: 20px;
  text-align: center; 
  font-size: 16px;
}

#forgot-password-wrapper span {
  font-size: 16px;
  color: #006489;
}

#forgot-password-wrapper span:hover {
  text-decoration: underline;
  color: #006489;
}

.alert.alert-error {
  background-color: #f86464;
  border-color: #f86464;
  color: #fff;
  font-weight: 300;
  text-align: center; }

.alert {
  padding: 7px;
  margin-bottom: 0;
  border-radius: 3px; }

/* .login-button-spacer {
  padding-top: 34px; } */

.custom-message-header {
  width: 406px;
  margin-top: 24px;
  margin-left: -53px;
  font-size: 22px;
  font-weight: 300;
  text-align: center; }

.custom-message-content {
  width: 406px;
  margin-top: 25px;
  margin-left: -53px;
  font-size: 16px;
  text-align: center; }

#kc-reset-password-form {
    width: 300px;
    margin: auto; }

.login-pf-page .card-pf p {
  color: #007fae; }

#kc-error-message, #kc-info-message {
  margin-top: 28px;
  text-align: center; }
  #kc-error-message .instruction, #kc-info-message .instruction {
    margin-bottom: 28px; }

#kc-form-login .form-group:first-child {
  padding-top: 4px; }

.main-login-form {
  margin-top: 24px; }

.login-pf-page .login-pf-settings {
  margin-bottom: 0 !important; }

#kc-locale ul {
  position: absolute;
  top: 20px;
  right: 0;
  display: none;
  min-width: 100px;
  padding: 2px 0;
  background-color: #fff;
  border: solid 1px #bbb;
  list-style: none; }

#kc-locale:hover ul {
  display: block;
  margin: 0; }

#kc-locale ul li a {
  display: block;
  padding: 5px 14px;
  color: #000 !important;
  line-height: 20px;
  text-decoration: none; }

#kc-locale ul li a:hover {
  background-color: #d4edfa;
  color: #4d5258; }

#kc-locale-dropdown a {
  padding: 0 15px 0 0;
  background: 0 0;
  color: #4d5258;
  font-weight: 300; }

#kc-locale-dropdown a:hover {
  text-decoration: none; }

a#kc-current-locale-link {
  display: block;
  padding: 0 5px; }

/* a#kc-current-locale-link:hover {
    background-color: rgba(0,0,0,0.2);
} */
a#kc-current-locale-link::after {
  margin-left: 4px;
  content: "\2c5"; }

.login-pf .container {
  padding-top: 40px; }

.login-pf a:hover,
a:hover {
  color: #007fae; }

#kc-logo {
  width: 100%; }

#kc-logo-wrapper {
  height: 63px;
  width: 300px;
  margin: 62px auto 0; }

div.kc-logo-text {
  height: 63px;
  width: 300px;
  margin: 0 auto; }

div.kc-logo-text span {
  display: none; }

div.qm-logo-text {
  height: 100px;
  width: 245px;
  margin: 0 auto;
  background-image: url(../img/logo.svg);
  background-repeat: no-repeat;
}

#kc-header {
  color: #ededed;
  overflow: visible;
  white-space: nowrap; }

#kc-header-wrapper {
  height: 145px;
  padding: 62px 10px 20px;
  font-size: 29px;
  letter-spacing: 3px;
  line-height: 1.2em;
  text-transform: uppercase;
  white-space: normal; }

#kc-content {
  width: 100%; 
  margin-top: -50px;
}

/* #kc-content-wrapper {
    overflow-y: hidden;
} */
#kc-info {
  padding-bottom: 200px;
  margin-bottom: -200px; }

#kc-info-wrapper {
  margin-top: 0;
  margin-bottom: 1rem;
  text-align: center !important;
  color: #006489 !important;
  font-size: 15.90px;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  box-sizing: inherit; }

#kc-form-options span {
  display: block;
  margin-top: 25px;
  text-align: center; }

#kc-form-options .checkbox {
  margin-top: 0;
  color: #007fae; }

#kc-terms-text {
  margin-bottom: 20px; }

#kc-registration {
  margin-bottom: 15px; }

/* TOTP */
ol#kc-totp-settings {
  padding-left: 20px;
  margin: 0; }

ul#kc-totp-supported-apps {
  margin-bottom: 10px; }

#kc-totp-secret-qr-code {
  max-height: 150px;
  max-width: 150px; }

#kc-totp-secret-key {
  padding: 10px 0;
  background-color: #fff;
  color: #333;
  font-size: 16px; }

/* OAuth */
#kc-oauth h3 {
  margin-top: 0; }

#kc-oauth ul {
  padding: 0;
  margin: 0;
  list-style: none; }

#kc-oauth ul li {
  padding: 10px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 12px; }

#kc-oauth ul li:first-of-type {
  border-top: 0; }

#kc-oauth .kc-role {
  display: inline-block;
  width: 50%; }

/* Code */
#kc-code textarea {
  height: 8em;
  width: 100%; }

/* Social */
#kc-social-providers ul {
  padding: 0; }

#kc-social-providers li {
  display: block; }

#kc-social-providers li:first-of-type {
  margin-top: 0; }

.zocial,
a.zocial {
  width: 100%;
  background: #f5f5f5;
  border: 0;
  border-radius: 0;
  color: #007fae;
  font-size: 14px;
  font-weight: normal;
  white-space: normal;
  text-shadow: none; }

.zocial:before {
  margin-right: 0;
  border-right: 0; }

.zocial span:before {
  padding: 7px 10px;
  font-size: 14px; }

.zocial:hover {
  background: #ededed !important; }

.zocial.facebook,
.zocial.github,
.zocial.google,
.zocial.microsoft,
.zocial.stackoverflow,
.zocial.linkedin,
.zocial.twitter {
  background-image: none;
  border: 0;
  box-shadow: none;
  text-shadow: none; }

/* Copy of zocial windows classes to be used for microsoft's social provider button */
.zocial.microsoft:before {
  content: "\f15d"; }

.zocial.stackoverflow:before {
  color: inherit; }

@media (min-width: 768px) {
  #kc-container-wrapper {
    position: absolute;
    width: 100%; }
  .login-pf .container {
    padding-right: 80px; }
  #kc-locale {
    position: relative;
    z-index: 9999;
    text-align: right; } }

@media (max-width: 767px) {
  #kc-header {
    padding-left: 15px;
    padding-right: 15px;
    text-align: left;
    float: none; }
  #kc-header-wrapper {
    height: 90px;
    padding: 20px 60px 0 0;
    color: #007fae;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0; }
  div.kc-logo-text {
    height: 90px;
    width: 150px;
    margin: 0;
    background-size: 100%; }
  #kc-form {
    float: none; }
  #kc-info-wrapper {
    padding-top: 15px;
    padding-left: 0;
    padding-right: 15px;
    margin-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1); }
  #kc-social-providers li {
    display: block;
    margin-right: 5px; }
  .login-pf .container {
    padding-top: 15px;
    padding-bottom: 15px; }
  #kc-locale {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 200px;
    text-align: right; }
  #kc-logo-wrapper {
    height: 21px;
    width: 100px;
    margin: 20px 0 0 20px;
    background-size: 100px 21px; }
  #forgot-password-wrapper {
    /* margin-top: 76px;  */
  } 
  }

@media (min-height: 646px) {
  #kc-container-wrapper {
    bottom: 12%; } }

@media (max-height: 645px) {
  #kc-container-wrapper {
    top: 20%;
    padding-top: 50px; } }

.card-pf form.form-actions .btn {
  margin-left: 10px;
  float: right; }

#kc-form-buttons {
  height: 41px;
  margin-top: 30px;
  text-align: center; }

.login-pf-page .login-pf-brand {
  width: 40%;
  max-width: 360px;
  margin-top: 20px; 
}

.card-pf {
  box-sizing: content-box;
  width: 300px;
  padding: 0 20px;
  margin: 0 auto;
  background: transparent;
  border-top: 0;
  box-shadow: 0 0 0; }

/*tablet*/
@media (max-width: 840px) {
  .login-pf-page .card-pf {
    padding: 20px 20px 30px 20px; } }

@media (max-width: 767px) {
  #kc-info-wrapper {
    width: 300px;
    margin-left: auto; }
  .custom-message-header {
    width: 300px;
    margin-left: auto; }
  .custom-message-content {
    width: 300px;
    margin-left: auto; }
  .login-pf-page .card-pf {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0; } }

.login-pf-page .login-pf-signup {
  margin-top: 14px; }

#kc-content-wrapper .row {
  margin-left: 0;
  margin-right: 0; }

/* @media (min-width: 768px) {
  .login-pf-page .login-pf-social-section:first-of-type {
    padding-right: 39px;
    margin-right: -1px;
    border-right: 1px solid #d1d1d1; }
  .login-pf-page .login-pf-social-section:last-of-type {
    padding-left: 40px; }
  .login-pf-page .login-pf-social-section .login-pf-social-link:last-of-type {
    margin-bottom: 0; }
  .login-pf body {
    background-image: url("../img/stone-bottom.svg"), url("../img/cloud-1.svg"), url("../img/cloud-2.svg"), url("../img/cloud-3.svg");
    background-position: right bottom, 140px 151px, calc(100% - 158.2px) 119px, calc(100% - 94.4px) 106px;
    background-size: auto, 186.8px 95.8px, 280.8px 134.7px, 136.6px 89px; } } */

.login-pf-page .login-pf-social-link {
  margin-bottom: 25px; }

.login-pf-page .login-pf-social-link a {
  padding: 2px 0; }

.login-pf-page.login-pf-page-accounts {
  margin-left: auto;
  margin-right: auto; }

.login-pf-page .btn-primary {
  margin-top: 0; }

.login-pf-page {
  align-items: center;
  max-height: 100vh;
}
#kc-header {
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  margin-bottom: 0!important;
}
/* @media (max-width: 767px){
  .login-pf-page {
    align-items: flex-start;
  }
} */
@media (max-height: 640px){
  .login-pf-page{
    top: -6rem;
  }
}
@media (max-height: 640px){
  .login-pf-page{
    align-items:flex-start;
    top: 0;
  }
}

.login-pf-page .card-pf {
  padding: 0 !important
}

#kc-form-wrapper {
  width: 300px;
  margin: auto;
  position: relative;
  z-index: 99;
}

.inputDiv {
  margin-top: 5px;
  margin-bottom: 10px;
}

.form-control::-webkit-input-placeholder {
  color: #006489;
}

.form-control::-moz-placeholder {
  color: #006489;
}

.form-control:-ms-input-placeholder {
  color: #006489;
}

.form-control:-moz-placeholder {
  color: #006489;
}

.form-control:placeholder {
  color: #006489;
}
button,
input,
optgroup,
select,
textarea {
    font-family: 'Roboto', sans-serif;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill{
    -webkit-box-shadow: inset 0 0 0px 9999px #F8FCFF;
    -moz-box-shadow: inset 0 0 0px 9999px #F8FCFF;
    -ms-box-shadow: inset 0 0 0px 9999px #F8FCFF;
    box-shadow: inset 0 0 0px 9999px #F8FCFF;
    border-color: #F8FCFF !important;
}

input:-webkit-autofill:focus,
textarea:-webkit-autofill:focus,
select:-webkit-autofill:focus{
    border-color: #5cb3fd !important;
}
.wave-top {
  background: url('../img/wave-top.svg') repeat-x 0 0;
  height: 181px;
  -webkit-animation: move-left 2s linear infinite;
  animation: move-left 2s linear infinite;
}

.wave-mid {
  background: url('../img/wave-mid.svg') repeat-x 0 0;
  height: 133px;
  -webkit-animation: move-right 2s linear infinite;
  animation: move-right 2s linear infinite;
}

.wave-bottom {
  background: url('../img/wave-bottom.svg') repeat-x 0 0;
  height: 72px;
}

.stone {
  position: absolute;
  bottom: 0;
  right: 0;
}

.stone-bottom {
  background: url('../img/stone-bottom.svg') no-repeat right bottom;
  height: 177px;
  width: 60%;
}

.stone-top {
  background: url('../img/tower-stone.svg') no-repeat right bottom;
  height: 456px;
  width: 40%;
  bottom: 100px;
}

@-webkit-keyframes move-left {
  from {
      background-position: -38px 0;
  }
  to {
      background-position: 0 0;
  }
}

@keyframes move-left {
  from {
      background-position: -38px 0;
  }
  to {
      background-position: 0 0;
  }
}

@-webkit-keyframes move-right {
  from {
      background-position: 38px 0;
  }
  to {
      background-position: 0 0;
  }
}

@keyframes move-right {
  from {
      background-position: 38px 0;
  }
  to {
      background-position: 0 0;
  }
}

@-webkit-keyframes flip {
  0% {}
  100% {
      -webkit-transform: rotateX(0deg);
      opacity: 1;
  }
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

/* Animation Css End */

[class^="qs-icon-"],
[class*=" qs-icon-"] {
background-image: url('../images/sprite-small-icon.svg');
background-repeat: no-repeat;
background-position: 0 0;
display: inline-block;
width: 20px;
height: 20px;
}

.backTo {
  font-size: 16px;
}
.kc-form-wrapper {
  width: 300px;
  margin: 0 auto;
}

.account-link-sso {
  max-width: 480px;
  margin: 0 auto;
  text-align: center;
}

.heading, #instruction1 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 15px;
}

#instruction2 > a {
  text-transform: lowercase;
}

.text {
  margin: 0;
}

#updateProfile, .hide-verification-message {
  display: none;
}

#linkAccount {
  background: #007FAE;
  color: white;
  margin-top: 24px;
}

.instruction {
  text-align: center;
  margin: 0 auto;
}

#instruction3 {
  display: none;
}

#kc-otp-login-form > .form-group {
  align-items: center;
}

#kc-otp-login-form > .form-group > div:first-child {
  padding-right: 10px;
}

#kc-otp-login-form > .form-group > div:last-child > input#otp {
  border-radius: 0.5rem;
}
