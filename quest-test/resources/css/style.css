@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,700');

html {
    height: 100%;
}

body {
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    color: #00263A;
    height: 100%;
}

h1 {
    font-size: 30px;
    font-weight: 400;
}

h2 {
    font-size: 28px;
    font-weight: 400;
}

ul,
li {
    margin: 0;
    padding: 0;
    list-style: none;
}

.pos-rel {
    position: relative;
}

.pos-abs {
    position: absolute;
}

body.login {
    background: #CBEDFD;
    height: auto;
}

#app {
    height: 100%;
}


/* Login Screen Css */

.login-container,
.login-form {
    width: 400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.login-form {
    width: 300px;
    position: relative;
    z-index: 6;
}

.logo {
    width: 245px;
    margin: 35% auto 30px;
}

.login-form .form-group {
    margin-bottom: 10px;
    text-align: center;
}

button,
input,
optgroup,
select,
textarea {
    font-family: '<PERSON><PERSON>', sans-serif;
}

input[type=text],
input[type=search],
input[type=email],
input[type=url],
input[type=tel],
input[type=number],
input[type=datetime-local],
input[type=date],
input[type=month],
input[type=week],
input[type=time],
input[type=password] {
    border-color: transparent;
    font-size: 16px;
    color: #000;
    background: #F8FCFF;
}

.form-control::-webkit-input-placeholder {
    color: #006489;
}

.form-control::-moz-placeholder {
    color: #006489;
}

.form-control:-ms-input-placeholder {
    color: #006489;
}

.form-control:-moz-placeholder {
    color: #006489;
}

.form-control:placeholder {
    color: #006489;
}

.btn-inside,
.btn-inside-right {
    position: absolute;
    z-index: 999;
    width: 33px;
    height: 38px;
    box-sizing: border-box;
    border: 0;
    background: transparent;
    cursor: pointer;
}
.map-headerbar .btn-inside,
.map-headerbar .btn-inside-right {
    height: 35px;
}
.btn-inside:focus,
.btn-inside-right:focus {
    outline: none;
}

.btn-inside-right {
    right: 0;
}

.btn-inside+input.form-control {
    padding-left: 32px;
}

.btn-inside+.btn-inside-right+input.form-control {
    padding-left: 32px;
    padding-right: 32px;
}

button,
input[type=button],
input[type=submit],
input[type=reset] {
    cursor: pointer;
}

.btn {
    -webkit-transition: padding 0.2s ease-in-out, min-width 0.2s ease-in-out, font-size 0.2s ease-in-out, color 0.2s ease-in-out, border-radius 0.2s ease-in-out;
    transition: padding 0.2s ease-in-out, min-width 0.2s ease-in-out, font-size 0.2s ease-in-out, color 0.2s ease-in-out, border-radius 0.2s ease-in-out;
}

.btn-primary {
    background: #CBEDFD;
    border: 1px solid #006489;
    border-radius: 3px;
    color: #00263A;
    min-width: 161px;
}

.btn-primary.active,
.btn-primary:hover,
.btn-primary:active,
.btn-primary:focus {
    color: #fff;
    background: #006489;
    border: 1px solid #006489;
    border-radius: 3px;
    -webkit-transition: background-color 0.2s ease-in-out;
    transition: background-color 0.2s ease-in-out;
}

.btn-primary[readonly],
.btn-primary[readonly]:focus,
.btn-primary[readonly]:hover {
    background: #CBEDFD;
    border: 1px solid #006489;
    text-shadow: none;
    opacity: 0.3;
}

.btn-link,
.btn-link:hover {
    color: #006489;
}

.btn[readonly=true] {
    opacity: 0.3;
    cursor: not-allowed;
}
/* Animation Css */
@-webkit-keyframes move-left {
    from {
        background-position: -38px 0;
    }
    to {
        background-position: 0 0;
    }
}

@keyframes move-left {
    from {
        background-position: -38px 0;
    }
    to {
        background-position: 0 0;
    }
}

@-webkit-keyframes move-right {
    from {
        background-position: 38px 0;
    }
    to {
        background-position: 0 0;
    }
}

@keyframes move-right {
    from {
        background-position: 38px 0;
    }
    to {
        background-position: 0 0;
    }
}

@-webkit-keyframes flip {
    0% {}
    100% {
        -webkit-transform: rotateX(0deg);
        opacity: 1;
    }
}

/* Animation Css End */

[class^="qs-icon-"],
[class*=" qs-icon-"] {
  background-image: url('../images/sprite-small-icon.svg');
  background-repeat: no-repeat;
  background-position: 0 0;
  display: inline-block;
  width: 20px;
  height: 20px;
}
.qs-icon-cross-red {
    background-position: -440px -40px;
    width: 12px;
    height: 12px;
}
.qs-icon-check-green {
    background-position: -420px -40px;
    width: 13px;
    height: 10px;
}

.wave {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.width-full {
    width: 100%;
}

.height-full {
    height: 100%;
}

.text-success {
    color: #2DC766 !important;
}
.gutter0{
    margin: 0;
}
.gutterB60 {
    margin-bottom: 60px;
}

.gutterB35 {
    margin-bottom: 35px;
}

.gutterB20 {
    margin-bottom: 20px;
}
.gutterB9 {
    margin-bottom: 9px;
}
.gutterB0 {
    margin-bottom: 0;
}

.gutterR5 {
    margin-right: 5px;
}
.gutterR6 {
    margin-right: 6px;
}
.gutterR8 {
    margin-right: 8px;
}
.gutterR10 {
    margin-right: 10px;
}

.gutterR14 {
    margin-right: 14px;
}

.gutterR15 {
    margin-right: 15px;
}

.gutterR20 {
    margin-right: 20px;
}

.gutterR35 {
    margin-right: 35px;
}

.gutterL30 {
    margin-left: 30px;
}

.gutterL20 {
    margin-left: 20px;
}

.gutterL10 {
    margin-left: 10px;
}

.gutterL6 {
    margin-left: 6px;
}

.gutterL5 {
    margin-left: 5px;
}

.gutterLR65 {
    margin-left: 65px;
    margin-right: 65px;
}

.offsetL130 {
    padding-left: 130px;
}

.gutterLR100 {
    margin-left: 100px;
    margin-right: 100px;
}

.gutterLR130 {
    margin-left: 130px;
    margin-right: 130px;
}

.gutterT4 {
    margin-top: 4px;
}

.gutterT6 {
    margin-top: 6px;
}

.gutterT7 {
    margin-top: 7px;
}
.gutterT10N{
    margin-top: -10px;
}
.gutterT8 {
    margin-top: 8px;
}
.gutterT12{
    margin-top: 12px;
}
.gutterT15 {
    margin-top: 15px;
}

.gutterT20 {
    margin-top: 20px;
}

.gutterT23 {
    margin-top: 23px;
}

.gutterT30 {
    margin-top: 30px;
}

.gutterT35 {
    margin-top: 35px;
}

.gutterT40 {
    margin-top: 40px;
}

.gutterT50 {
    margin-top: 50px;
}

.gutterT60 {
    margin-top: 60px;
}

.offsetT60 {
    padding-top: 60px;
}

.shiftL40 {
    margin-left: -40px;
}

.shiftL35p {
    margin-left: -35%;
}

.border-left-light-blue {
    border-left: 1px solid #CBEDFD;
}
.font-20 {
    font-size: 20px;
}

.wave-top {
    background: url('../images/wave-top.svg') repeat-x 0 0;
    height: 181px;
    -webkit-animation: move-left 2s linear infinite;
    animation: move-left 2s linear infinite;
}

.wave-mid {
    background: url('../images/wave-mid.svg') repeat-x 0 0;
    height: 133px;
    -webkit-animation: move-right 2s linear infinite;
    animation: move-right 2s linear infinite;
}

.wave-bottom {
    background: url('../images/wave-bottom.svg') repeat-x 0 0;
    height: 72px;
}

.stone {
    position: absolute;
    bottom: 0;
    right: 0;
}

.stone-bottom {
    background: url('../images/stone-bottom.svg') no-repeat right bottom;
    height: 177px;
    width: 60%;
}

.stone-top {
    background: url('../images/tower-stone.svg') no-repeat right bottom;
    height: 456px;
    width: 40%;
    bottom: 100px;
}

.tooltip {
    white-space: nowrap;
    min-width: 148px;
    -webkit-transition: all 0.5s ease-in-out;
    -webkit-transition: opacity 0.5s ease-in-out;
    transition: opacity 0.5s ease-in-out;
    text-align: left;
    padding: 4px 10px;
    border-radius: .25rem;
    font-size: 1rem;
    font-family: 'Roboto', sans-serif;
    border: 1px solid #CBEDFD;
    background: #fff;
    color: rgb(46, 46, 53);
    font-weight: 400;
}

.tooltip-parents {
    position: relative;
}

.tooltip-parents .tooltip {
    display: none;
}

.tooltip-parents:hover .tooltip {
    -webkit-animation: tooltip-animation 0.3s ease-in-out forwards !important;
    animation: tooltip-animation 0.3s ease-in-out forwards !important;
    display: block;
}

.input-error .tooltip {
    opacity: 1;
    background: #fff;
}

.tooltip.tooltip-right {
    left: 100%;
    top: 0;
    padding: 2px 10px;
    margin: 5px 10px;
}

.tooltip.tooltip-left {
    right: 100%;
    top: 0;
    padding: 2px 10px;
    margin: 3px 5px 5px 10px;
}

.tooltip.tooltip-bottom {
    left: 50%;
    top: 100%;
    padding: 2px 10px;
    margin: 5px 10px 5px -50px;
}

.tooltip::before {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.tooltip-right::before {
    top: 50%;
    left: -5px;
    margin-top: -4px;
    content: "";
    border-width: 5px 5px 5px 0;
    border-right-color: #00263A;
}

.tooltip-left::before {
    top: 50%;
    right: -4px;
    margin-top: -5px;
    content: "";
    border-width: 5px 0 5px 5px;
    border-left-color: #00263A;
}

.input-error .tooltip-right::before {
    top: 50%;
    left: -5px;
    margin-top: -4px;
    content: "";
    border-width: 5px 5px 5px 0;
    border-right-color: #3CD2FA;
}

.tooltip-bottom::before {
    top: -4px;
    left: 50%;
    content: "";
    border-width: 0 5px 5px 5px;
    border-bottom-color: #00263A;
    margin-left: -3px;
}

.icon-set .tooltip {
    min-width: 100px;
    text-align: center;
}


/* Login Screen Css End */


/* Forgot Password contaner */

.text-info {
    color: #006489 !important;
}

.text-info-light {
    color: #066587;
}

.text-danger {
    color: #FF4E4E;
}


/* Forgot Password contaner */


/* Button Effect Css*/

.btn-primary {
    position: relative;
    overflow: hidden;
    transform: translate3d(0,0,0);
}

.btn-primary:after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: -webkit-radial-gradient(circle, #000 10%, transparent 10.01%);
    background-image: radial-gradient(circle, #000 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    -webkit-transform: scale(10,10);
    transform: scale(10,10);
    opacity: 0;
    -webkit-transition: opacity 1s, -webkit-transform .5s;
    transition: opacity 1s, -webkit-transform .5s;
    transition: transform .5s, opacity 1s;
    transition: transform .5s, opacity 1s, -webkit-transform .5s;
}

.btn-primary:active:after {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: .2;
    -webkit-transition: 0s;
    transition: 0s;
}


/* Button Effect Css End */
