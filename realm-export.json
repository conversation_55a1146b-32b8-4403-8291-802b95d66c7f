{"id": "c148887b-88fb-43ef-9e7e-4ef386d8bf83", "realm": "marine", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "eef93bf8-4717-4cdb-8a46-a97dad9a651e", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "c148887b-88fb-43ef-9e7e-4ef386d8bf83", "attributes": {}}, {"id": "f7dea3d7-2f36-413d-a84b-45f59146bd54", "name": "default-roles-marine", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "c148887b-88fb-43ef-9e7e-4ef386d8bf83", "attributes": {}}, {"id": "e8df7725-590a-49a3-b815-dc8a4941430d", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "c148887b-88fb-43ef-9e7e-4ef386d8bf83", "attributes": {}}], "client": {"realm-management": [{"id": "63b9486b-51e6-4cb0-9b13-226e5ef6af7e", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "1fb12190-2de4-4ea9-a4e9-c1a542d7978a", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "4168388a-8d0c-4a5f-b75c-c4397e94f142", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "cf75fea2-aeb5-421a-915c-61c0d6d7d97b", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "62977e44-a3ed-438f-93b3-ff6025aad5cd", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-realm", "manage-identity-providers", "query-users", "manage-events", "view-identity-providers", "manage-clients", "view-clients", "view-events", "query-realms", "manage-authorization", "query-clients", "create-client", "view-realm", "query-groups", "view-users", "impersonation", "view-authorization", "manage-users"]}}, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "a1337efc-764d-4a85-be6c-b6c3dae24cb7", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "ff1227d7-2922-4ec2-ba15-b753e9ea2112", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "9f420075-5a59-49ba-aba7-35b020b98f51", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "09048f42-a4d5-492d-87b1-94867855c0ab", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "59a12f5f-04bf-4509-bd9a-489a3277fa78", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "396c214a-6163-4f7d-a093-b0cbc2743b83", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "bcbf90a2-abe7-49f4-9611-3cdc0849737f", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "50d30ec1-7624-4a05-9a53-ad24975bde2f", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "b968f34d-0b55-4968-a51e-6b87c2791865", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "30790991-1faa-4f46-a728-ce6dbbe9748e", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "ed2359a3-294b-49c7-879d-270e715c41b0", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "360beb55-7b24-4710-8541-effc634b3e4d", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "6eb8f227-885a-4cb2-a630-1fad98491922", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}, {"id": "8d1dc452-c057-4f6c-bf8d-0c6be342ea50", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "150a6c18-88c1-4093-83bd-73ac4de050f9", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "e981d726-f483-460c-af47-f84146399734", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "dd25e55f-4a13-4b05-8cff-d9851c3d6ffd", "attributes": {}}], "account": [{"id": "88ea34d4-4500-48b0-9160-b9c5d42e7f70", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "fa04ab05-6b62-40e4-93ab-acde5bbecfb2", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "0bb2933b-da46-4655-8260-90fbbd4b7f6e", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "5893e18b-6a31-4f35-beea-2a115c1611e7", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "8040bd90-db2a-4591-95fb-df0dd1bef237", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "8433af8f-d145-4b46-9d04-a4b7193ab007", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}, {"id": "716b027a-1213-4805-be87-e947b8a26a14", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "14013da8-665a-4f1f-8181-06599a829b09", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "f7dea3d7-2f36-413d-a84b-45f59146bd54", "name": "default-roles-marine", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "c148887b-88fb-43ef-9e7e-4ef386d8bf83"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "14013da8-665a-4f1f-8181-06599a829b09", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/marine/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/marine/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "749ee4f6-86b0-488b-9715-08d1a196d51b", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/marine/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/marine/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "9fb59fd1-66fe-4f9a-a5c5-98ce43618356", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "3824ba1a-7340-473a-b8eb-0b7c03bcfcbd", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "dd25e55f-4a13-4b05-8cff-d9851c3d6ffd", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "150a6c18-88c1-4093-83bd-73ac4de050f9", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9602ffb1-af29-4dd3-b29b-88f63c3e4d36", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/marine/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/marine/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "f21e8aab-f545-4ac2-984c-3aceb1455625", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "c146cdbc-1fd3-45b5-80fb-f9ed06c48fdc", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "e8bf552c-60d7-4e39-a010-d593a8bd0594", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "8c8a5295-9348-4eb2-9406-a8a802f41e08", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "56d14ec1-fa77-4089-aa03-358d163fdded", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "f94adb70-e6cd-4203-9270-638e0239dcf3", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "e0262658-25fc-4ed2-ad23-3f425d21fd32", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "98d671a5-a1a5-4416-b8c2-e8c0aeef4024", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "16cb4910-f604-47e5-bce4-2da4d36ca7d8", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "bfc9e459-92d7-4939-8175-83f28b5c7761", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "53b25f92-504b-4e6d-a2de-498771cac8ba", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "cece0be4-21cd-4105-ad12-a2bdbaa062fa", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "61f3798a-1b39-403e-80c2-289de501b29a", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "ad525d5e-0451-4c0c-8f30-5a65da2d5433", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "36b3fbf8-cff9-4c45-810f-07e28ae806b7", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "a8462e57-b82d-44e0-89f0-fc3f1e4b5ba5", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "c7c90600-82be-46e9-9b9d-89c98870abd6", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "dab3c753-b935-4e3f-8787-9493820352a2", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "f88bfd36-4063-49b8-8944-e45fb2b93815", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "363061c0-4edb-4a7e-a5db-c2bf7d03f485", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "7f52efb3-c2b2-4d62-997c-0d50fed2813f", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "72ce6a49-ddff-41c0-a408-d4b02f10a70e", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "3e5d9d30-85be-4673-8199-c3be22392f03", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "dcba8394-14f8-48dd-be36-3f419a84e201", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "04185d76-3c6f-47a5-9036-d80ea83ad56a", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}]}, {"id": "169a3fd1-fe9c-4b24-b6fb-6273ac893d74", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "84b1d3bc-ee4d-43d1-9789-9a9e7bcd330e", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "a860c901-d9d6-49c8-8871-55fdfe49003d", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "31c5b09b-1b3a-40a6-9ebf-c25f97ce795d", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "5f35d224-b830-4fe4-ad0a-aa0ad67a3439", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "e82407e1-3fa9-4315-b7cd-2a8c2816ada9", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "9daa2b81-8c08-4a02-8bd0-5e63434a4a35", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "68d94a6c-57d2-4ea6-9f82-8ca8cba8a603", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "e12ef3c9-9ae3-4124-af33-31e1447fda7e", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "f361889f-0575-4e6c-8a60-2ac45eec3375", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "2d87d70f-a587-4f0a-a2a7-f9160aed6f0c", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "6d190288-ee78-411d-86a1-2419b7c9a960", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "2b6e2e79-bfea-4118-88b1-722a7b195d8f", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}], "defaultDefaultClientScopes": ["email", "acr", "role_list", "profile", "web-origins", "roles"], "defaultOptionalClientScopes": ["offline_access", "address", "microprofile-jwt", "phone"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "4884bdd4-04fe-4c63-b343-6d8565c736b6", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "3a68f0a8-1b78-4f23-9dae-38b51ece605e", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-address-mapper", "saml-role-list-mapper", "saml-user-property-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-user-attribute-mapper"]}}, {"id": "053ca73a-dac4-47ae-9988-fa6571c52360", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "fd4abac2-67a2-4489-b19d-fe81b9b22adb", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "5d514441-c089-453b-a9d7-63c92571d2bc", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-property-mapper", "saml-user-property-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "saml-user-attribute-mapper"]}}, {"id": "05c3d8fe-bc38-49fa-a897-bb2a8dd46f97", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "9e6b5b28-de9d-46ba-8a56-0145f6fa9e89", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "57f2fd7f-4e30-4b68-96f8-2e0dfafa59c1", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "42731e05-c1f4-4b32-9fb6-5c3f4e5d0975", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "50c41e21-38e0-4e6b-a174-5f45869d20ce", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "7b0fb8a9-29bf-4c01-9fdd-d843ac57ada6", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "cd83dd31-e583-4539-a84a-dde630b43ac9", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "96520d59-e147-4193-ae64-738217d8174d", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "4da0b424-cd73-4de4-ac2d-abf82fc43c03", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "528c7911-707b-4d1e-a925-3ad89e4bd5f0", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "0c0b7d06-f62c-4df0-b2c3-9c4a1c07d8c7", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f4dc06c5-e1d9-48ba-bc0a-a75a19908dcf", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2281a5c0-897b-46d1-ad0e-5931c1d20416", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "4d77e64c-2145-4980-864c-4f38b1f955fd", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "d7041117-2e8c-4521-85b8-277276c4c077", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "c600b90e-7ffb-40df-8e97-50a86463baff", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "e4be497b-d5dc-4302-b861-52823580ad39", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "d548c5d7-051f-42ae-8b1f-6c5e489acb85", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "94f389e8-d810-452e-aa23-d6823c2a8eb8", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "898718c3-db35-4028-ad9f-83526b57b0da", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "dd573a7b-67d2-4ef7-afa2-bcb98950e1c5", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "********-1a99-4bf6-8601-fde788fe5da9", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "7b53a734-537d-4b0d-b5ac-b4bf800782ea", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "c27d5906-e9ab-4268-8c19-50b57bc519dc", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "4a5d0e5e-c4d1-4ef2-a9f4-2d8b7aff339e", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "222759ed-5a83-4061-a195-b1b631a6fa31", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "0fbf21f2-52e9-43e9-936c-47dfb571a00b", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "338af25f-e7c2-498c-b5c9-380fefd8d3b6", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "59719ddd-70b3-41c3-8ed3-c2403e9bcdfe", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "parRequestUriLifespan": "60", "cibaInterval": "5"}, "keycloakVersion": "19.0.0", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}