package com.example.sample_event_listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.common.util.concurrent.MoreExecutors;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;

import org.keycloak.events.Event;
import org.keycloak.events.EventListenerProvider;
import org.keycloak.events.admin.AdminEvent;
import com.example.sample_event_listener.dto.QuestMessageEnvelope;
import com.example.sample_event_listener.dto.QuestMessageType;

import org.keycloak.events.EventType;

import java.util.HashMap;
import java.util.Map;



public class CustomEventListenerProvider implements EventListenerProvider {

    private final Publisher publisher;

    private static final ObjectMapper objectMapper = new ObjectMapper();



    public CustomEventListenerProvider(Publisher publisher) {
        this.publisher = publisher;
    }
    @Override
    public void onEvent(AdminEvent adminEvent, boolean includeRepresentation) {
        try {

            String resourcePath = adminEvent.getResourcePath();
            String realmId = adminEvent.getRealmId();
            String userId = extractUserId(resourcePath);

            System.out.println("=== Admin Event Fired ===");
            System.out.printf("Resource: %s | Operation: %s | Path: %s%n", adminEvent.getResourceType(), adminEvent.getOperationType(), resourcePath);

            if (adminEvent.getResourceType().name().equals("USER")) {
                switch (adminEvent.getOperationType()) {
                    case CREATE:
                        System.out.println("[User Created] => UserID: " + userId + ", RealmID: " + realmId);

                        sendToPubSub(QuestMessageType.ADMIN_CREATE, userId, realmId);
                        break;

                    case UPDATE:
                        System.out.println("[User Updated] => UserID: " + userId + ", RealmID: " + realmId);

                        sendToPubSub(QuestMessageType.UPDATE, userId, realmId);
                        break;

                    default:

                }
            }
            if (adminEvent.getResourceType().name().equals("REALM_ROLE_MAPPING")) {

                switch (adminEvent.getOperationType()) {
                    case CREATE:
                        System.out.println("[Role Assigned] => UserID: " + userId + ", RealmID: " + realmId);

                        sendToPubSub(QuestMessageType.ROLE_ASSIGNED, userId, realmId);
                        break;

                    case DELETE:
                        System.out.println("[Role Deleted] => UserID: " + userId + ", RealmID: " + realmId);

                        sendToPubSub(QuestMessageType.ROLE_DELETED, userId, realmId);
                        break;

                    default:

                }
            }

        } catch (Exception e) {
            System.err.println("Error processing AdminEvent: " + e.getMessage());
            e.printStackTrace();
        }
    }


    @Override
    public void onEvent(Event event) {
        try {
            EventType eventType = event.getType();

            if (eventType == EventType.REGISTER || eventType == EventType.UPDATE_PASSWORD) {
                System.out.println("==[ Captured Key Event ]==");
                System.out.println("Event Type: " + eventType);
                System.out.println("User ID: " + event.getUserId());
                System.out.println("Realm ID : " + event.getRealmId());

                QuestMessageType questMessageType = QuestMessageType.valueOf(eventType.name());

                sendToPubSub(questMessageType, event.getUserId(), event.getRealmId());
            }

        } catch (Exception e) {
            System.err.println("Error processing UserEvent: " + e.getMessage());
            e.printStackTrace();
        }
    }


    private void sendToPubSub(QuestMessageType messageType, String userId, String realmId) {
        try {

            Map<String,Object> messageBody= new HashMap<>();
            messageBody.put("userId",userId);
            messageBody.put("realmId",realmId);
            QuestMessageEnvelope envelope = QuestMessageEnvelope.of(
                    messageBody,
                    messageType,
                    null
            );

            String json = objectMapper.writeValueAsString(envelope);

            PubsubMessage message = PubsubMessage.newBuilder()
                    .setData(ByteString.copyFromUtf8(json))
                    .build();

            ApiFuture<String> future = publisher.publish(message);
            ApiFutures.addCallback(
                    future,
                    new ApiFutureCallback<String>() {
                        @Override
                        public void onSuccess(String messageId) {
                            System.out.printf("Published to topic '%s' with message ID: %s%n", publisher.getTopicNameString(), messageId);
                        }

                        @Override
                        public void onFailure(Throwable t) {
                            System.err.println("Failed to publish to Pub/Sub: " + t.getMessage());
                            t.printStackTrace();
                        }
                    },
                    MoreExecutors.directExecutor()
            );
        } catch (Exception e) {
            System.err.println("Failed to publish to Pub/Sub: " + e.getMessage());
            e.printStackTrace();
        }
    }


    private String extractUserId(String resourcePath) {
        // Extract userId from any path starting with "users/{userId}"
        if (resourcePath != null && resourcePath.startsWith("users/")) {
            String[] parts = resourcePath.split("/");
            return parts.length >= 2 ? parts[1] : null;
        }
        return null;
    }



    @Override
    public void close() {

    }

}