package com.example.sample_event_listener;

import com.google.cloud.pubsub.v1.Publisher;
import com.google.pubsub.v1.TopicName;
import org.keycloak.Config;
import org.keycloak.events.EventListenerProvider;
import org.keycloak.events.EventListenerProviderFactory;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.KeycloakSessionFactory;
import com.google.cloud.ServiceOptions;

import io.grpc.LoadBalancerRegistry;
import io.grpc.internal.PickFirstLoadBalancerProvider;
import io.grpc.NameResolverRegistry;
import io.grpc.internal.DnsNameResolverProvider;

import java.io.IOException;

public class CustomEventListenerProviderFactory implements EventListenerProviderFactory {

    private Publisher publisher;

    static {
        try {
            System.out.println("Initializing gRPC providers...");

            // Register the pick_first load balancer provider
            LoadBalancerRegistry.getDefaultRegistry().register(new PickFirstLoadBalancerProvider());

            // Register DNS name resolver provider
            NameResolverRegistry.getDefaultRegistry().register(new DnsNameResolverProvider());

            System.out.println("gRPC providers registered successfully");

        } catch (Exception e) {
            System.err.println("Warning: Could not register gRPC providers: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void init(Config.Scope config) {
        try {

            String projectId = System.getenv("GCP_PROJECT_ID");
            String topic = System.getenv("PUBSUB_TOPIC");


//               String projectId = "prj-nonprod-eng-svc-01";
//               String topic = "hull-user-spi";

            TopicName topicName = TopicName.of(projectId, topic);
            publisher = Publisher.newBuilder(topicName).build();
            System.out.printf("Pub/Sub Publisher initialized for topic: %s%n", topicName.toString());
        } catch (IOException e) {
            throw new RuntimeException("Failed to create Pub/Sub publisher", e);
        }
    }
    @Override
    public EventListenerProvider create(KeycloakSession session) {
        return new CustomEventListenerProvider(publisher);
    }

    @Override
    public void postInit(KeycloakSessionFactory factory) {

    }

    @Override
    public void close() {
        if (publisher != null) {
            try {
                publisher.shutdown();
            } catch (Exception ignored) {}
        }
    }

    @Override
    public String getId() {
        return "user-sync-event-listener";
    }
 }



