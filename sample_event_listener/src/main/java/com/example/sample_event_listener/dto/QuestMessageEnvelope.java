package com.example.sample_event_listener.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Data
public class QuestMessageEnvelope implements Serializable {

    private static final long serialVersionUID = 128698760738744369L;

    private String messageId;
    private String messageSender;
    private List<String> intendedReceivers;
    private QuestMessageType messageType;
    private Object messageBody;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private Date sentAt = new Date();
    private String tenantID;


    public static QuestMessageEnvelope of(Object body, QuestMessageType type, String tenantID) {
        QuestMessageEnvelope envelope = new QuestMessageEnvelope();
        envelope.setMessageBody(body);
        envelope.setMessageType(type);
        envelope.setMessageId(UUID.randomUUID().toString());
        envelope.setTenantID(tenantID);
        envelope.setMessageSender("keycloak");
        return envelope;
    }

}
